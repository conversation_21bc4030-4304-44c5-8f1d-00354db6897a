"""Economic evaluation helper functions

Note: This module is currently not actively used in the project's main workflow,
as economic calculations are primarily performed through the Gemini API. However,
these utility functions are retained for:

1. Future feature extensions (e.g., automated calculation verification)
2. Potential fallback calculations when API is unavailable
3. Reference implementations for economic formulas

The functions provide precise calculations for:
- Market equilibrium
- Price elasticity
- Producer/Consumer surplus
- Tax effects and deadweight loss
"""

import re
from typing import Tuple, Optional

def parse_linear_function(expression: str) -> Tuple[float, float]:
    """Parse linear function of form ax + b or x = ay + b"""
    # Remove spaces and convert to standard form
    expr = expression.replace(" ", "").lower()
    if "=" in expr:
        expr = expr.split("=")[1]
    
    # Extract coefficients
    match = re.match(r"(-?\d*\.?\d*)([a-z])([+-]\d*\.?\d*)?", expr)
    if not match:
        raise ValueError(f"Invalid linear expression: {expression}")
    
    a = match.group(1)
    b = match.group(3) or "0"
    
    return (float(a if a and a != "-" else "-1" if a == "-" else "1"),
            float(b))

def calculate_equilibrium(demand: str, supply: str) -> Tuple[float, float]:
    """Calculate market equilibrium from demand and supply functions"""
    # Parse demand function (Qd = a1P + b1)
    a1, b1 = parse_linear_function(demand)
    
    # Parse supply function (Qs = a2P + b2)
    a2, b2 = parse_linear_function(supply)
    
    # Solve for P: a1P + b1 = a2P + b2
    # (a1 - a2)P = b2 - b1
    p = (b2 - b1) / (a1 - a2)
    
    # Calculate Q
    q = a1 * p + b1
    
    return p, q

def calculate_elasticity(p1: float, p2: float, q1: float, q2: float) -> float:
    """Calculate price elasticity of demand"""
    avg_p = (p1 + p2) / 2
    avg_q = (q1 + q2) / 2
    
    pct_change_q = (q2 - q1) / avg_q
    pct_change_p = (p2 - p1) / avg_p
    
    return pct_change_q / pct_change_p

def calculate_surplus(p_floor: float, demand: str, supply: str) -> Tuple[float, float, float]:
    """Calculate producer surplus, consumer surplus, and deadweight loss"""
    # Get demand and supply quantities at floor price
    a1, b1 = parse_linear_function(demand)
    a2, b2 = parse_linear_function(supply)
    
    qd = a1 * p_floor + b1
    qs = a2 * p_floor + b2
    
    surplus = qs - qd
    
    return surplus

def calculate_tax_effects(tax: float, demand: str, supply: str) -> Tuple[float, float, float, float]:
    """Calculate equilibrium price, quantity, tax revenue, and deadweight loss with tax"""
    # Original equilibrium
    p_orig, q_orig = calculate_equilibrium(demand, supply)
    
    # Adjust supply curve for tax
    a2, b2 = parse_linear_function(supply)
    supply_with_tax = f"{a2}P + {b2 - (a2 * tax)}"
    
    # New equilibrium
    p_new, q_new = calculate_equilibrium(demand, supply_with_tax)
    
    # Tax revenue
    tax_revenue = tax * q_new
    
    # Deadweight loss (triangle formula)
    dwl = 0.5 * (p_new - (p_new - tax)) * (q_orig - q_new)
    
    return p_new, p_new - tax, q_new, tax_revenue, dwl