"""Exam marker package initialization"""

name = "exam-marker"
__version__ = "0.1.0"

from .marker import ExamMarker
import asyncio

async def main():
    from .__main__ import main as _main
    await _main()

def run():
    """Entry point for the application"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExam marking cancelled by user")
    except Exception as e:
        print(f"\nError during exam marking: {str(e)}")
        raise

__all__ = ["ExamMarker", "run"]
