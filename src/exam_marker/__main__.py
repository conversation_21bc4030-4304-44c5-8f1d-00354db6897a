"""Main entry point for the exam marker application"""
import argparse
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog
import os
import sys
import time
from pathlib import Path
import asyncio
from datetime import datetime
from dotenv import load_dotenv
import google.generativeai as genai
from .marker import ExamMarker

def get_marker_name():
    root = tk.Tk()
    root.withdraw()
    marker_name = simpledialog.askstring("Marker Name", "Please enter your name (this will appear on the feedback):")
    if not marker_name:
        messagebox.showerror("Error", "Marker name is required. The program will exit.")
        sys.exit(1)
    return marker_name

def select_exam_directory():
    root = tk.Tk()
    root.withdraw()
    messagebox.showinfo("Select Exam Directory", "Please select the directory containing all exam PDFs")
    dir_path = filedialog.askdirectory(
        title="Select directory containing exam PDFs",
        mustexist=True
    )
    if not dir_path:
        messagebox.showerror("Error", "No directory selected. The program will exit.")
        sys.exit(1)
    return dir_path

async def main():
    load_dotenv()
    start_time = time.time()

    # Parse arguments first
    parser = argparse.ArgumentParser(description='Grade economics exams using Gemini API')
    parser.add_argument('-i', '--input-dir', help='Directory containing exam PDFs (optional in GUI mode)')
    parser.add_argument('-d', '--output-dir', help='Directory for output files')
    parser.add_argument('-m', '--marker-name', help='Name of the marker')
    parser.add_argument('--gui', action='store_true', help='Run in GUI mode')
    parser.add_argument('--version', action='version', version='%(prog)s 0.1.0')
    args = parser.parse_args()

    # Get marker name
    marker_name = args.marker_name if args.marker_name else get_marker_name()

    # Configure Gemini API
    api_key = os.environ.get("GEMINI_API_KEY")
    if not api_key:
        messagebox.showerror("Error", "GEMINI_API_KEY not found in .env file")
        sys.exit(1)

    genai.configure(api_key=api_key)
    generation_config = {
        "temperature": 1,
        "top_p": 0.95,
        "top_k": 40,
        "max_output_tokens": 8192,
        "response_mime_type": "text/plain",
    }
    model = genai.GenerativeModel(
        model_name="gemini-2.5-flash-preview-04-17",
        generation_config=generation_config,
    )

    # Get input/output directories
    if not args.input_dir:  # GUI mode
        pdf_dir = select_exam_directory()
        output_dir = pdf_dir  # Save outputs in the same directory
    else:  # CLI mode
        pdf_dir = args.input_dir
        output_dir = args.output_dir or pdf_dir

    # Initialize marker
    marker = ExamMarker(model=model, marker_name=marker_name, gemini_api_key=api_key)

    # Process all PDFs in directory
    pdf_files = sorted(Path(pdf_dir).glob("*.pdf"))
    if not pdf_files:
        error_msg = f"No PDF files found in {pdf_dir}"
        if not args.input_dir:  # GUI mode
            messagebox.showerror("Error", error_msg)
        else:
            print(f"Error: {error_msg}")
        sys.exit(1)

    print(f"\nFound {len(pdf_files)} exam PDFs to process")
    all_feedback = []

    for i, pdf_path in enumerate(pdf_files, 1):
        print(f"\nProcessing exam {i}/{len(pdf_files)}: {pdf_path.name}")

        try:
            try:
                # Process PDF without showing dialogs
                feedback = await marker.process_pdf(str(pdf_path))
            except Exception as e:
                print(f"Error in graph analysis: {str(e)}\nThe system will continue with text-only analysis.")

            # Save individual feedback
            output_path = Path(output_dir) / f"{pdf_path.stem}_feedback.md"
            output_path.write_text(feedback)
            print(f"Saved feedback to {output_path}")

            # Add to combined feedback
            all_feedback.append(feedback)
            all_feedback.append("\n---\n")

        except Exception as e:
            error_msg = f"Error processing {pdf_path.name}: {str(e)}"
            print(error_msg)
            if not args.input_dir:  # GUI mode
                messagebox.showerror("Error", error_msg)

    # Save combined feedback
    combined_path = Path(output_dir) / "all_exam_feedback.md"
    combined_feedback = "\n".join(all_feedback)
    combined_path.write_text(combined_feedback)

    # Show completion statistics
    end_time = time.time()
    processing_time = end_time - start_time

    stats_msg = (
        f"\nExam Marking Complete!"
        f"\n- Total exams processed: {len(pdf_files)}"
        f"\n- Processing time: {processing_time:.1f} seconds"
        f"\n- Average time per exam: {processing_time/len(pdf_files):.1f} seconds"
        f"\n\nResults saved to: {output_dir}"
    )

    if not args.input_dir:  # GUI mode
        messagebox.showinfo("Complete", stats_msg)
    else:
        print(stats_msg)

def run_main():
    """Synchronous wrapper for the async main function"""
    asyncio.run(main())

if __name__ == "__main__":
    run_main()
