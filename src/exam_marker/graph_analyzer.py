import os
from google import generativeai as genai
import PIL.Image
from pathlib import Path
import io
import fitz  # PyMuPDF
import math
import numpy as np
import re  # Added import for regular expressions
from typing import Dict, Any, Union, Tuple, Optional, List
 
class GraphAnalyzer:
    def __init__(self, api_key: str):
        """Initialize the GraphAnalyzer with Gemini API credentials."""
        # Configure Gemini API
        genai.configure(api_key=api_key)

        # Load analysis prompt from environment variable
        self.analysis_prompt = os.getenv('GRAPH_ANALYSIS')
        if not self.analysis_prompt:
            print("Warning: GRAPH_ANALYSIS environment variable not found. Using default prompt.")
            self.analysis_prompt = """Analyze this economics graph/diagram carefully and thoroughly. Look for ALL visual elements, even if faint or hand-drawn:

1. GRAPH TYPE IDENTIFICATION:
   * Determine the specific economic graph type (supply/demand curves, IS-LM model, production possibilities frontier, indifference curves, etc.)
   * For hand-drawn or unclear graphs, focus on the structural patterns (curves, lines, intersections) to determine the type
   * NEVER respond with "Unknown" - instead, identify the most likely graph type based on visible structure and economic context
   * Consider both standard types (supply-demand, cost curves) and specialized economic diagrams

2. AXIS IDENTIFICATION (Critical Task):
   * X-axis: Carefully identify ANY labels, scales, units or notations - even if partially visible or handwritten
   * Y-axis: Same approach as X-axis
   * Look for any numbers, variables, or economic terms near axes (P, Q, GDP, etc.)
   * Check for handwritten notes, arrows, or annotations near axes that indicate their meaning
   * If an axis appears unlabeled, describe what it LIKELY represents based on the graph type
   * Look for any scale markings (ticks, grid lines) that provide measurement context

3. KEY ELEMENTS DETECTION:
   * Trends: Describe ALL visual patterns - rising curves, falling lines, intersections, equilibrium points
   * Key points: Identify ALL notable features - intersections, maxima/minima, labeled points, shaded areas
   * Look for any handwritten annotations explaining specific points or movements
   * Check for arrows showing shifts or movements of curves/lines
   * Identify any labeled regions or areas (consumer/producer surplus, deadweight loss)

4. ECONOMIC ANALYSIS:
   * Explain what economic concept or relationship the graph likely represents
   * Identify if the graph shows equilibrium, optimization, or comparative scenarios
   * Determine what economic variables are being related or compared
   * Note any policy implications or economic outcomes demonstrated

5. QUALITY ASSESSMENT:
   * Evaluate clarity and completeness of the graph (well-drawn vs. rough sketch)
   * Note any issues with labeling, scaling, or visual presentation
   * Assess whether the graph effectively demonstrates the economic concept

Format your response EXACTLY as follows (with specific content for each section):
Graph Type: [specific economic graph type - NEVER use "Unknown"]
X-axis: [label/description, include partial text if visible]
Y-axis: [label/description, include partial text if visible]
Trends: [comma-separated list of all trends/patterns]
Key Points: [comma-separated list of all notable points/features]
Economic Interpretation: [concise explanation of economic concept represented]
Quality Assessment: [brief evaluation of graph effectiveness and clarity]

If you are absolutely certain the image shows no economics graph/diagram, respond with "NOT_A_GRAPH".
"""

        self.model = genai.GenerativeModel(
            model_name="gemini-2.5-flash-preview-04-17",
            generation_config={
                "temperature": 0.1,
                "top_p": 0.95,
                "top_k": 40,
                "max_output_tokens": 8192,
                "response_mime_type": "text/plain",
            }
        )

    def _upload_image(self, image: PIL.Image.Image) -> dict:
        """Convert PIL Image to base64 encoded data for Gemini API"""
        import base64
        import tempfile

        try:
            # Create a temporary file with delete=True に変更して確実に削除
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=True) as temp_file:
                # Save image to the temporary file
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                image.save(temp_file.name, format="JPEG", quality=95)
                temp_path = temp_file.name

                # with ブロック内で処理することで、自動的に削除される
                # Upload image with Gemini API
                try:
                    file = genai.upload_file(temp_path, mime_type="image/jpeg")
                    print(f"Successfully uploaded image to Gemini API")

                    # Return the image data formatted for API
                    return {
                        "inline_data": {
                            "mime_type": "image/jpeg",
                            "data": file
                        }
                    }
                except Exception as e:
                    print(f"Error uploading image to Gemini API: {str(e)}")

                    # Fallback to base64 encoding if API upload fails
                    with open(temp_path, "rb") as f:
                        img_bytes = f.read()

                    return {
                        "inline_data": {
                            "mime_type": "image/jpeg",
                            "data": base64.b64encode(img_bytes).decode('utf-8')
                        }
                    }
        finally:
            # tempfile.NamedTemporaryFile(delete=True)は自動的にファイルを削除するため、
            # 追加のクリーンアップは不要
            pass

    async def extract_images_from_pdf(self, pdf_path: str, page_num: int) -> list[PIL.Image.Image]:
        """Extract all potential graph images from a specific page of a PDF."""
        doc = None
        images = []

        try:
            # Open PDF with error handling
            try:
                doc = fitz.open(pdf_path)
            except Exception as e:
                raise ValueError(f"Failed to open PDF: {str(e)}")

            # Validate page number
            if page_num >= len(doc):
                raise ValueError(f"Page number {page_num} out of range (total pages: {len(doc)})")

            page = doc[page_num]

            # Validate page content
            if not page:
                raise ValueError(f"Page {page_num} is empty or invalid")

            # Step 1: Try to extract embedded images first
            print(f"Extracting images from page {page_num}...")
            images_found = set()  # Track processed images

            try:
                for image_info in page.get_images():
                    try:
                        xref = image_info[0]
                        if (xref in images_found):
                            continue

                        base_image = doc.extract_image(xref)
                        if not base_image:
                            continue

                        image_bytes = base_image.get("image")
                        if not image_bytes:
                            continue

                        pil_image = PIL.Image.open(io.BytesIO(image_bytes))
                        print(f"Found embedded image: {pil_image.size}")

                        # サイズベースのプレフィルタリング（極めて小さい画像は除外）
                        if pil_image.width < 50 or pil_image.height < 50:
                            print(f"✗ Skipping tiny image: {pil_image.size}")
                            continue

                        if self._is_potential_graph(pil_image):
                            images.append(pil_image)
                            images_found.add(xref)
                            print(f"✓ Added graph image: {pil_image.size}")

                    except Exception as e:
                        print(f"Could not process image: {str(e)}")
                        continue

            except Exception as e:
                print(f"Error in embedded image extraction: {str(e)}")

            # Step 2: If no embedded images found, analyze page content with enhanced region detection
            if not images:
                print("No embedded images found, analyzing page content...")

                try:
                    # Get page dimensions
                    rect = page.rect
                    width = rect.width
                    height = rect.height

                    # 重複検出を防ぐための領域管理
                    detected_regions = []

                    def is_overlapping(box1, box2, threshold=0.5):
                        """領域の重複を確認"""
                        x1, y1, w1, h1 = box1
                        x2, y2, w2, h2 = box2

                        x_overlap = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                        y_overlap = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                        overlap_area = x_overlap * y_overlap
                        min_area = min(w1 * h1, w2 * h2)

                        return overlap_area / min_area > threshold

                    def find_content_boundaries(pix: fitz.Pixmap) -> Optional[Tuple[int, int, int, int]]:
                        """グラフ領域の境界を検出（改良版）"""
                        try:
                            # ピクセルデータを取得
                            img = PIL.Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                            img_array = np.array(img)

                            # グレースケールに変換
                            gray = np.mean(img_array, axis=2)

                            # 非白色ピクセルの位置を取得（より厳密な閾値）
                            content = gray < 250

                            if not np.any(content):
                                return None

                            # 内容のある行と列を特定（連続性を考慮）
                            rows = np.sum(content, axis=1) > pix.width * 0.03  # 行の3%以上が非白（より低い閾値）
                            cols = np.sum(content, axis=0) > pix.height * 0.03  # 列の3%以上が非白（より低い閾値）

                            # 連続した領域を検出
                            row_regions = np.where(rows)[0]
                            col_regions = np.where(cols)[0]

                            if len(row_regions) == 0 or len(col_regions) == 0:
                                return None

                            # 境界を取得
                            top = row_regions[0]
                            bottom = row_regions[-1]
                            left = col_regions[0]
                            right = col_regions[-1]

                            # 境界に余裕を持たせる（アスペクト比を考慮）
                            width = right - left
                            height = bottom - top
                            margin = min(width, height) * 0.1  # 小さい方の10%をマージンに

                            top = max(0, int(top - margin))
                            bottom = min(pix.height, int(bottom + margin))
                            left = max(0, int(left - margin))
                            right = min(pix.width, int(right + margin))

                            return (left, top, right, bottom)

                        except Exception as e:
                            print(f"Error in boundary detection: {str(e)}")
                            return None

                    # まず全体を1つのグラフとして検討
                    try:
                        full_page = page.get_pixmap(matrix=fitz.Matrix(2, 2), alpha=False)
                        full_page_img = PIL.Image.frombytes("RGB", [full_page.width, full_page.height], full_page.samples)

                        # ページ全体をグラフとして検証（ページの大きさが適切であれば）
                        if 200 <= full_page.width <= 2000 and 200 <= full_page.height <= 2000:
                            if self._is_potential_graph(full_page_img):
                                print(f"✓ Full page recognized as graph: {full_page_img.size}")
                                images.append(full_page_img)
                                return images  # 全体がグラフの場合は早期リターン
                    except Exception as e:
                        print(f"Error analyzing full page: {str(e)}")

                    # グラフの可能性が高い領域を検索（より精密に）
                    search_regions = []

                    # より段階的な検索（複数のレベルでスキャン）
                    # 1. まず全体から内容のある領域を特定
                    full_pix = page.get_pixmap(matrix=fitz.Matrix(2, 2), alpha=False)
                    try:
                        boundaries = find_content_boundaries(full_pix)
                        if boundaries:
                            left, top, right, bottom = boundaries
                            # 領域の妥当性チェック
                            region_width = right - left
                            region_height = bottom - top
                            aspect_ratio = region_width / region_height

                            # より柔軟なアスペクト比の制限
                            if (0.2 <= aspect_ratio <= 5.0 and  # アスペクト比の範囲を拡大
                                region_width >= 200 and         # 最小幅
                                region_height >= 150):          # 最小高さ

                                # Matrix(2, 2)の拡大を考慮して座標を調整
                                adj_region = (
                                    max(0, left / 2),
                                    max(0, top / 2),
                                    min(width, right / 2),
                                    min(height, bottom / 2)
                                )
                                search_regions.append(adj_region)
                    except Exception as e:
                        print(f"Error in primary boundary detection: {str(e)}")

                    # 2. 領域が見つからない場合は一般的なレイアウトパターンを試す
                    if not search_regions:
                        # パターン1: 中央部分を探索（60%の領域）
                        margin_w = width * 0.2  # 左右20%ずつマージン
                        margin_h = height * 0.2 # 上下20%ずつマージン
                        search_regions.append((
                            margin_w,
                            margin_h,
                            width - margin_w,
                            height - margin_h
                        ))

                        # パターン2: ページの上半分
                        search_regions.append((
                            width * 0.1,
                            height * 0.05,
                            width * 0.9,
                            height * 0.5
                        ))

                        # パターン3: ページの下半分
                        search_regions.append((
                            width * 0.1,
                            height * 0.5,
                            width * 0.9,
                            height * 0.95
                        ))

                    # 検出された領域を重複なく処理
                    for region in search_regions:
                        try:
                            # 既存の検出領域との重複をチェック
                            current_box = (region[0], region[1], region[2]-region[0], region[3]-region[1])
                            is_duplicate = any(is_overlapping(current_box, r) for r in detected_regions)

                            if is_duplicate:
                                continue

                            clip = fitz.Rect(*region)
                            pix = page.get_pixmap(
                                matrix=fitz.Matrix(2, 2),
                                clip=clip,
                                alpha=False
                            )

                            if pix.width < 100 or pix.height < 100:  # 最小サイズ条件
                                continue

                            img = PIL.Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

                            if self._is_potential_graph(img):
                                images.append(img)
                                detected_regions.append(current_box)
                                print(f"✓ Found unique graph in region: {img.size}")

                        except Exception as e:
                            print(f"Error processing region: {str(e)}")
                            continue

                except Exception as e:
                    print(f"Error in region analysis: {str(e)}")

            # Step 3: If still no images but page has limited text, try full page as last resort
            if not images:
                try:
                    # ページのテキスト量を確認
                    page_text = page.get_text("text")
                    word_count = len(page_text.split())

                    # テキストが少ない場合（おそらく図やグラフが中心）
                    if word_count < 100:
                        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2), alpha=False)
                        if pix.width >= 100 and pix.height >= 100:
                            img = PIL.Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                            if self._is_potential_graph(img):
                                images.append(img)
                                print("✓ Found graph in full page (text-sparse page)")
                except Exception as e:
                    print(f"Error in text-based page analysis: {str(e)}")

        finally:
            doc.close()

        return images

    def _detect_axes(self, pixels: list, width: int, height: int) -> dict:
        """軸の検出を行い、スコアを返す"""
        scores = {'h': 0, 'v': 0}

        # 画像サイズに応じた閾値調整
        density_threshold = 0.15
        transition_ratio = 0.08

        # 水平軸の検出（優先領域：下部1/3とその周辺）
        search_regions_h = [
            (height - height//3, height, 2.0),     # 下部1/3（高優先度）
            (height//2, height - height//3, 1.0),  # 中下部（中優先度）
            (height//3, height//2, 0.5)           # 中上部（低優先度）
        ]

        for start_y, end_y, weight in search_regions_h:
            for y in range(start_y, end_y):
                dark_pixels = 0
                transitions = 0
                prev_dark = False

                for x in range(width):
                    idx = y * width + x
                    if idx >= len(pixels):
                        continue

                    is_dark = pixels[idx] < 128
                    if is_dark:
                        dark_pixels += 1
                    if is_dark != prev_dark:
                        transitions += 1
                    prev_dark = is_dark

                # 水平軸の条件判定
                if (dark_pixels > width * density_threshold and
                    transitions < width * transition_ratio):
                    scores['h'] += weight

        # 垂直軸の検出（優先領域：左側1/3とその周辺）
        search_regions_v = [
            (0, width//3, 2.0),                # 左側1/3（高優先度）
            (width//3, width//2, 1.0),         # 中左部（中優先度）
            (width//2, 2*width//3, 0.5)        # 中央部（低優先度）
        ]

        for start_x, end_x, weight in search_regions_v:
            for x in range(start_x, end_x):
                dark_pixels = 0
                transitions = 0
                prev_dark = False

                for y in range(height):
                    idx = y * width + x
                    if idx >= len(pixels):
                        continue

                    is_dark = pixels[idx] < 128
                    if is_dark:
                        dark_pixels += 1
                    if is_dark != prev_dark:
                        transitions += 1
                    prev_dark = is_dark

                # 垂直軸の条件判定
                if (dark_pixels > height * density_threshold and
                    transitions < height * transition_ratio):
                    scores['v'] += weight

        return scores

    def _detect_graph_features(self, pixels: list, width: int, height: int, params: dict) -> dict:
        """グラフの特徴（曲線、データポイントなど）を検出"""
        features = {
            'curves': [],
            'points': [],
            'max_line_len': 0
        }

        current_line_len = 0
        cell_size = params['cell_size']
        step = params['step']

        # 中央部分の走査
        for y in range(cell_size, height - cell_size, step):
            for x in range(cell_size, width - cell_size, step):
                if pixels[y * width + x] < 128:
                    neighbors = []
                    for dy in [-1, 0, 1]:
                        for dx in [-1, 0, 1]:
                            if dx == 0 and dy == 0:
                                continue
                            ny, nx = y + dy, x + dx
                            if 0 <= ny < height and 0 <= nx < width:
                                neighbors.append(pixels[ny * width + nx])

                    dark_neighbors = sum(1 for n in neighbors if n < 128)
                    if dark_neighbors == 1:  # 端点
                        features['points'].append((x, y))
                        current_line_len = 0
                    elif 2 <= dark_neighbors <= 3:  # 曲線
                        features['curves'].append((x, y))
                        current_line_len += 1
                        features['max_line_len'] = max(
                            features['max_line_len'],
                            current_line_len
                        )
                    else:
                        current_line_len = 0

        return features


    def _print_debug_info(self, axis_scores: dict, features: dict) -> None:
        """デバッグ情報を出力"""
        print("Graph analysis:")
        print(f"- Axis scores: h={axis_scores['h']}, v={axis_scores['v']}")
        print(f"- Curves: {len(features['curves'])}")
        print(f"- Points: {len(features['points'])}")
        print(f"- Max line length: {features['max_line_len']}")

        # 追加の特徴情報
        if 'curves' in features and features['curves']:
            avg_curve_len = sum(len(c) for c in features['curves']) / len(features['curves'])
            print(f"- Average curve length: {avg_curve_len:.2f}")

        # 特徴点の分布情報
        if 'points' in features and features['points']:
            x_coords = [p[0] for p in features['points']]
            y_coords = [p[1] for p in features['points']]
            if x_coords and y_coords:
                x_range = max(x_coords) - min(x_coords)
                y_range = max(y_coords) - min(y_coords)
                print(f"- Point distribution: x-range={x_range}, y-range={y_range}")

    def _validate_image_size(self, width: int, height: int) -> bool:
        """画像のサイズと形状を検証"""
        MIN_DIMENSION = 100   # 最小サイズを小さく
        MIN_AREA = 20000     # 最小面積を小さく
        MAX_AREA = 1500000   # 最大面積を大きく

        if width < MIN_DIMENSION or height < MIN_DIMENSION:
            print(f"Image too small: {width}x{height}")
            return False

        if width * height < MIN_AREA or width * height > MAX_AREA:
            print(f"Invalid image area: {width * height}")
            return False

        aspect_ratio = width / height
        if aspect_ratio < 0.25 or aspect_ratio > 4.0:  # アスペクト比の制約を緩和
            print(f"Invalid aspect ratio: {aspect_ratio:.2f}")
            return False

        return True

    def _detect_background_regions(self, image: PIL.Image.Image) -> bool:
        """背景色の違いからグラフ/図の領域を検出（感度向上版）"""
        # RGBに変換
        rgb_image = image.convert('RGB')
        width, height = image.size
        pixels = list(rgb_image.getdata())

        # より細かいグリッドで分析（精度向上）
        grid_size = 20  # より細かいグリッド
        cell_width = max(2, width // grid_size)
        cell_height = max(2, height // grid_size)

        # 背景色の候補を取得（より多くのサンプル）
        border_samples = []
        # 上下左右の境界から色をサンプリング
        for i in range(0, width, cell_width):
            border_samples.extend([
                pixels[i],                    # 上端
                pixels[width * (height-1) + i]  # 下端
            ])
        for i in range(0, height, cell_height):
            border_samples.extend([
                pixels[i * width],             # 左端
                pixels[i * width + width-1]    # 右端
            ])

        # 最も頻出する色を背景色とする
        if not border_samples:
            return False

        bg_color = max(set(border_samples), key=border_samples.count)

        # 色の差異を判定する閾値（より柔軟に）
        color_threshold = 50  # より寛容な閾値

        def is_similar_color(c1, c2):
            return all(abs(x - y) <= color_threshold for x, y in zip(c1, c2))

        # 領域ごとの背景色との差異をカウント
        different_regions = 0
        content_regions = 0
        edge_density = 0  # エッジ検出のための変数

        # 前の行との色差分を記録
        prev_row_colors = [None] * ((width-1) // cell_width)

        for y in range(0, height-cell_height, cell_height):
            for x in range(0, width-cell_width, cell_width):
                # セル内の色を分析
                cell_colors = []
                for cy in range(cell_height):
                    for cx in range(cell_width):
                        idx = (y + cy) * width + (x + cx)
                        if idx < len(pixels):
                            cell_colors.append(pixels[idx])

                if not cell_colors:
                    continue

                # セルの代表色（最頻出色）を取得
                cell_color = max(set(cell_colors), key=cell_colors.count)

                # 背景色と異なる領域をカウント
                if not is_similar_color(cell_color, bg_color):
                    different_regions += 1

                # エッジ検出（色の急激な変化を検出）
                cell_idx = x // cell_width
                if cell_idx < len(prev_row_colors) and prev_row_colors[cell_idx]:
                    if not is_similar_color(cell_color, prev_row_colors[cell_idx]):
                        edge_density += 1

                # 内容のある領域（完全な白や黒でない）をカウント
                if not (is_similar_color(cell_color, (255,255,255)) or
                        is_similar_color(cell_color, (0,0,0))):
                    content_regions += 1

                # 現在の行の色を記録
                cell_idx = x // cell_width
                if cell_idx < len(prev_row_colors):
                    prev_row_colors[cell_idx] = cell_color

        total_regions = ((height-1) // cell_height) * ((width-1) // cell_width)
        if total_regions == 0:
            return False

        # 比率計算
        different_ratio = different_regions / total_regions
        content_ratio = content_regions / total_regions
        edge_ratio = edge_density / total_regions if total_regions > 0 else 0

        # Debug information
        print(f"Background analysis:")
        print(f"- Different regions: {different_regions}/{total_regions} ({different_ratio:.2%})")
        print(f"- Content regions: {content_regions}/{total_regions} ({content_ratio:.2%})")
        print(f"- Edge density: {edge_ratio:.2%}")
        print(f"- Background color: RGB{bg_color}")

        # 背景色の特性を判定
        is_light_bg = all(c >= 230 for c in bg_color)
        is_dark_bg = all(c <= 25 for c in bg_color)
        is_mid_tone_bg = not (is_light_bg or is_dark_bg)

        # 判定条件の調整（より寛容に）
        has_valid_background = (
            # パターン1: 通常の背景分布
            (0.05 <= different_ratio <= 0.95 and content_ratio >= 0.02) or

            # パターン2: エッジが多い（グラフ線の可能性）
            (edge_ratio >= 0.05 and different_ratio >= 0.1) or

            # パターン3: 白背景での特別判定（より寛容に）
            (is_light_bg and 0.03 <= different_ratio <= 0.95 and content_ratio >= 0.01) or

            # パターン4: 暗い背景での特別判定
            (is_dark_bg and 0.05 <= different_ratio <= 0.9 and content_ratio >= 0.03) or

            # パターン5: 中間色背景（よりグラフらしい）
            (is_mid_tone_bg and 0.04 <= different_ratio <= 0.8 and content_ratio >= 0.02)
        )

        print(f"✓ 有効な背景パターンを検出" if has_valid_background else
              "✗ 有効な背景パターンなし")
        return has_valid_background

    def _get_grayscale_pixels(self, image: PIL.Image.Image) -> list:
        """グレースケール画像のピクセルデータを取得"""
        gray_image = image.convert('L')
        return list(gray_image.getdata())

    def _initialize_analysis_params(self, width: int, height: int) -> dict:
        """分析用パラメータを初期化"""
        return {
            'cell_size': min(width, height) // 4,
            'step': max(1, min(width, height) // 200)
        }

    def _detect_color_grid_patterns(self, image: PIL.Image.Image) -> dict:
        """
        カラー罫線や格子パターンを検出する

        Args:
            image: 分析対象のPIL画像

        Returns:
            dict: 検出結果を含む辞書
        """
        # RGB画像に変換
        rgb_image = image.convert('RGB')
        width, height = image.size

        # 検出結果の初期化
        result = {
            "has_grid": False,
            "grid_density": 0.0,
            "colored_lines": False,
            "grid_color": None,
            "grid_confidence": 0.0
        }

        try:
            # 画像をNumPy配列に変換
            np_image = np.array(rgb_image)

            # エッジ検出（Sobel演算子による簡易版）
            gray = np.mean(np_image, axis=2).astype(np.uint8)

            # 水平方向のエッジ検出
            h_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]])
            v_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]])

            # 畳み込み演算（簡易版）
            h_edges = np.zeros_like(gray)
            v_edges = np.zeros_like(gray)

            # エッジ検出の計算（効率のため一部のピクセルをサンプリング）
            sample_step = max(1, min(width, height) // 100)

            for y in range(1, height - 1, sample_step):
                for x in range(1, width - 1, sample_step):
                    # 各方向のエッジ強度を計算
                    h_sum = 0
                    v_sum = 0
                    for i in range(3):
                        for j in range(3):
                            if y+i-1 < height and x+j-1 < width:
                                pixel = gray[y+i-1, x+j-1]
                                h_sum += pixel * h_kernel[i, j]
                                v_sum += pixel * v_kernel[i, j]

                    h_edges[y, x] = abs(h_sum)
                    v_edges[y, x] = abs(v_sum)

            # エッジ強度の閾値（ゼロ除算防止のためのチェックを追加）
            h_threshold = np.percentile(h_edges[h_edges > 0], 75) if np.any(h_edges > 0) else 0
            v_threshold = np.percentile(v_edges[v_edges > 0], 75) if np.any(v_edges > 0) else 0

            # 有効な格子線をカウント
            h_lines = h_edges > h_threshold if h_threshold > 0 else np.zeros_like(h_edges, dtype=bool)
            v_lines = v_edges > v_threshold if v_threshold > 0 else np.zeros_like(v_edges, dtype=bool)

            h_line_count = np.sum(h_lines) / sample_step
            v_line_count = np.sum(v_lines) / sample_step

            # 格子密度の計算
            total_pixels = max(1, (width // sample_step) * (height // sample_step))  # ゼロ除算防止
            grid_density = (h_line_count + v_line_count) / (2 * total_pixels)

            # 色付きの線を検出する変数
            colored_edges = np.zeros((height, width), dtype=bool)
            detected_colors = []
            has_dashed_lines = False

            # 色のばらつきを分析
            color_variance = np.var(np_image.reshape(-1, 3), axis=0)
            has_color_variance = np.max(color_variance) > 400  # 閾値を下げて感度を上げる

            # 拡張された罫線色リスト（より多くの色を検出）
            grid_colors = [
                # 基本色
                (0, 0, 255),    # 青
                (255, 0, 0),    # 赤
                (0, 255, 0),    # 緑（明るい緑）
                (0, 128, 0),    # 緑（暗い緑）
                (128, 0, 128),  # 紫
                (128, 0, 0),    # 暗い赤/マルーン
                (0, 128, 128),  # ティール
                (128, 128, 0),  # オリーブ
                # 追加色
                (255, 165, 0),  # オレンジ
                (255, 0, 255),  # マゼンタ
                (0, 255, 255),  # シアン
                (165, 42, 42),  # 茶色
                (70, 130, 180), # スチールブルー
                (139, 69, 19),  # サドルブラウン
                (46, 139, 87)   # シーグリーン
            ]

            # 色付き罫線の検出（クラスタリングによる自動検出）
            if np_image.size > 0:
                try:
                    from sklearn.cluster import KMeans

                    # 画像サイズに応じたクラスタ数を決定
                    cluster_count = min(5, max(3, len(np_image.reshape(-1, 3)) // 10000 + 2))

                    # サンプリングしたピクセルでクラスタリングを実行
                    # Noneチェックを追加
                    reshaped_data = np_image.reshape(-1, 3)
                    if reshaped_data is not None and len(reshaped_data) > 0:
                        sampled_pixels = reshaped_data[
                            np.random.choice(
                                len(reshaped_data),
                                min(2000, len(reshaped_data)),
                                replace=False
                            )
                        ]

                        kmeans = KMeans(n_clusters=cluster_count, random_state=0, n_init=3).fit(sampled_pixels)

                    # クラスタの中心色と頻度を取得
                    centers = kmeans.cluster_centers_
                    labels = kmeans.predict(sampled_pixels)
                    counts = np.bincount(labels)

                    # 背景色（最も頻度の高い色）を特定
                    bg_idx = np.argmax(counts)
                    bg_color = centers[bg_idx].astype(int)

                    # 非背景色を罫線候補として抽出
                    line_colors = []
                    for i, center in enumerate(centers):
                        if i != bg_idx:
                            center_int = center.astype(int)

                            # 背景色との対比が強い色のみ考慮
                            contrast = np.sqrt(np.sum((center_int - bg_color)**2))
                            if contrast > 30:  # コントラスト閾値
                                line_colors.append(center_int)

                    # 線の色の分布を分析
                    for line_color in line_colors:
                        color_matches = []

                        # この色のピクセル位置を記録（破線検出用）
                        for y in range(0, height, sample_step):
                            for x in range(0, width, sample_step):
                                if y < height and x < width:
                                    pixel = np_image[y, x]
                                    # 色の距離を計算
                                    dist = np.sqrt(np.sum((pixel - line_color)**2))
                                    if dist < 40:  # より寛容な閾値
                                        colored_edges[y, x] = True
                                        color_matches.append((x, y))

                        # 十分なピクセルが見つかった場合、色を登録
                        if len(color_matches) > total_pixels * 0.01:  # 1%以上
                            detected_colors.append(tuple(line_color))

                            # 破線パターン検出
                            if len(color_matches) >= 10:
                                # X方向の連続性分析
                                x_matches = sorted([pos[0] for pos in color_matches])
                                if len(x_matches) > 1:
                                    x_diffs = [x_matches[i+1] - x_matches[i] for i in range(len(x_matches)-1)]

                                    # Y方向の連続性分析
                                    y_matches = sorted([pos[1] for pos in color_matches])
                                    if len(y_matches) > 1:
                                        y_diffs = [y_matches[i+1] - y_matches[i] for i in range(len(y_matches)-1)]

                                        # 差分の変動が少なく、一定のパターンがある場合は破線
                                        x_pattern = len(set(x_diffs)) <= 3 and np.std(x_diffs) < 5
                                        y_pattern = len(set(y_diffs)) <= 3 and np.std(y_diffs) < 5

                                        if x_pattern or y_pattern:
                                            has_dashed_lines = True

                except Exception as e:
                    print(f"K-means clustering warning: {e}")
                    # クラスタリングが失敗した場合は代替手法で検出

            # 既知の色パターン検出（クラスタリング失敗時のバックアップ）
            if not detected_colors:
                for grid_color in grid_colors:
                    matches = []

                    # より効率的なサンプリング
                    sample_step_color = max(1, sample_step * 2)

                    for y in range(0, height, sample_step_color):
                        for x in range(0, width, sample_step_color):
                            if y < height and x < width:
                                pixel = np_image[y, x]
                                # 色の距離を計算（HSV空間比較も考慮）
                                dist = np.sqrt(np.sum((pixel - grid_color)**2))
                                if dist < 60:  # より寛容な閾値で検出
                                    colored_edges[y, x] = True
                                    matches.append((x, y))

                    # 十分なマッチが見つかった場合
                    if len(matches) > total_pixels * 0.005:  # 0.5%以上
                        detected_colors.append(grid_color)

                        # 破線パターン検出（基本パターン）
                        if len(matches) >= 10:
                            x_pos = sorted([m[0] for m in matches])
                            y_pos = sorted([m[1] for m in matches])

                            if len(x_pos) > 2 and len(y_pos) > 2:
                                # 簡易的な破線検出
                                x_diffs = [x_pos[i+1] - x_pos[i] for i in range(len(x_pos)-1)]
                                y_diffs = [y_pos[i+1] - y_pos[i] for i in range(len(y_pos)-1)]

                                if (len(set(x_diffs[:10])) <= 3) or (len(set(y_diffs[:10])) <= 3):
                                    has_dashed_lines = True

            # 結果更新
            has_colored_lines = len(detected_colors) > 0 or np.sum(colored_edges) > total_pixels * 0.02

            # 格子パターンの信頼度
            grid_confidence = 0.0

            # 水平線と垂直線のスコア
            if h_line_count > width * 0.08 and v_line_count > height * 0.08:
                grid_confidence = min(1.0, (h_line_count / (width * 0.3) + v_line_count / (height * 0.3)) / 2)

            # 色付き罫線による信頼度上昇
            if has_colored_lines:
                grid_confidence = max(grid_confidence, 0.7)

                # 複数色検出による信頼度上昇
                if len(detected_colors) >= 2:
                    grid_confidence = max(grid_confidence, 0.8)

            # 破線検出による信頼度上昇
            if has_dashed_lines:
                grid_confidence = max(grid_confidence, 0.75)

            # 主要な色（最も検出数の多い色）
            primary_color = None
            if detected_colors:
                primary_color = detected_colors[0]

            result.update({
                "has_grid": grid_confidence > 0.5,
                "grid_density": grid_density,
                "colored_lines": has_colored_lines,
                "grid_color": primary_color,
                "grid_colors": detected_colors,  # 検出されたすべての色
                "grid_confidence": grid_confidence,
                "has_dashed_lines": has_dashed_lines  # 破線検出フラグ
            })

            # デバッグ情報表示
            print(f"Grid pattern analysis:")
            print(f"- Grid confidence: {grid_confidence:.2f}")
            print(f"- Grid density: {grid_density:.4f}")
            print(f"- Horizontal lines: {h_line_count:.0f}")
            print(f"- Vertical lines: {v_line_count:.0f}")
            print(f"- Has colored lines: {has_colored_lines}")
            print(f"- Detected colors: {len(detected_colors)}")
            print(f"- Has dashed lines: {has_dashed_lines}")
            if primary_color:
                print(f"- Primary grid color: RGB{primary_color}")

            return result

        except Exception as e:
            print(f"Error in grid detection: {str(e)}")
            return result

    async def analyze_graph(self, image: PIL.Image.Image, context: str = "") -> Dict[Any, Any]:
        """Analyze a graph using Gemini's vision capabilities."""
        import asyncio
        from concurrent.futures import TimeoutError
        import os

        prompt = self.analysis_prompt
        temp_path = None  # 一時ファイルパスを追跡するための変数を追加

        # グラフ画像の前処理
        try:
            # テキスト識別による早期フィルタリング (追加)
            pixels = self._get_grayscale_pixels(image)
            width, height = image.size
            params = self._initialize_analysis_params(width, height)
            features = self._detect_graph_features(pixels, width, height, params)
            text_scores = self._detect_text_characteristics(pixels, width, height, features)

            if text_scores['text_probability'] > 0.9:  # 修正: 閾値を0.85から0.9に引き上げ
                print(f"✗ Rejecting image: High probability of being text content ({text_scores['text_probability']:.2f})")
                return {
                    "success": False,
                    "error": f"Image appears to be text content rather than a graph (text probability: {text_scores['text_probability']:.2f})",
                    "text_probability": text_scores['text_probability'],
                    "raw_response": "FILTERING: Image rejected due to high text characteristics"
                }

            # 大きすぎる画像をリサイズ（Gemini APIの制限に合わせる）
            max_dimension = 1500
            original_width, original_height = image.size

            if original_width > max_dimension or original_height > max_dimension:
                # アスペクト比を維持しつつリサイズ
                ratio = min(max_dimension / original_width, max_dimension / original_height)
                new_width = int(original_width * ratio)
                new_height = int(original_height * ratio)

                print(f"Resizing image from {original_width}x{original_height} to {new_width}x{new_height}")
                image = image.resize((new_width, new_height), PIL.Image.LANCZOS)

            # 小さすぎる画像を拡大（より良い分析のため）
            min_dimension = 300
            width, height = image.size

            if width < min_dimension or height < min_dimension:
                # アスペクト比を維持しつつ拡大
                ratio = max(min_dimension / width, min_dimension / height)
                new_width = int(width * ratio)
                new_height = int(height * ratio)

                print(f"Enlarging small image from {width}x{height} to {new_width}x{new_height}")
                image = image.resize((new_width, new_height), PIL.Image.LANCZOS)

            # コントラスト強調と色調整（グラフの視認性を向上）
            from PIL import ImageEnhance, ImageOps

            # 色付きグラフの検出（前処理のヒントとして使用）
            grid_analysis = self._detect_color_grid_patterns(image)

            # カラー画像の場合はRGBに変換
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # グラフタイプに応じた前処理
            if grid_analysis["has_grid"] and grid_analysis["colored_lines"]:
                # 色付き罫線を強調
                color_enhancer = ImageEnhance.Color(image)
                image = color_enhancer.enhance(1.5)  # 彩度を上げる

                # コントラストを強調
                contrast_enhancer = ImageEnhance.Contrast(image)
                image = contrast_enhancer.enhance(1.3)
            else:
                # 通常のグラフの場合：コントラスト強調
                contrast_enhancer = ImageEnhance.Contrast(image)
                image = contrast_enhancer.enhance(1.2)

            # 明るさも少し調整
            brightness_enhancer = ImageEnhance.Brightness(image)
            image = brightness_enhancer.enhance(1.1)

        except Exception as e:
            print(f"Warning: Image preprocessing failed: {str(e)}")
            # 前処理に失敗した場合は元の画像を使用

        if context:
            prompt += f"\n\nContext: {context}"

        try:
            print(f"Starting graph analysis with Gemini API...")

            # Handle image upload with timeout
            try:
                # Import necessary modules at the beginning of the block
                import tempfile
                import os

                # 確実に削除するために「delete=True」に変更
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=True) as temp_file:
                    # Ensure image is RGB mode
                    if image.mode != 'RGB':
                        image = image.convert('RGB')
                    # Save with high quality
                    image.save(temp_file.name, format="JPEG", quality=95)
                    temp_path = temp_file.name

                    # 一時ファイルがオープンされている間に処理することで確実に削除される
                    try:
                        # Upload file to Gemini with timeout
                        file = await asyncio.wait_for(
                            asyncio.get_event_loop().run_in_executor(
                                None,
                                lambda: genai.upload_file(temp_path, mime_type="image/jpeg")
                            ),
                            timeout=30.0
                        )

                        print(f"Successfully uploaded graph image to Gemini API")

                    except Exception as e:
                        print(f"Error during file upload: {str(e)}")
                        raise e

                # tempfile.NamedTemporaryFileの場合、with句を抜けると自動的に削除される
                temp_path = None

            except TimeoutError:
                return {
                    "success": False,
                    "error": "Image upload timed out",
                    "raw_response": None
                }
            except Exception as e:
                print(f"Error uploading image: {str(e)}")
                return {
                    "success": False,
                    "error": f"Failed to upload image: {str(e)}",
                    "raw_response": None
                }
            try:
                # Create chat session with correct format
                chat = self.model.start_chat()

                # 改善されたプロンプト（グラフ検出精度向上）
                enhanced_prompt = prompt

                # グラフ特性に関する情報をプロンプトに追加
                if grid_analysis["has_grid"]:
                    color_info = ""
                    if grid_analysis["grid_color"]:
                        color_name = "blue" if grid_analysis["grid_color"][0] < 50 and grid_analysis["grid_color"][2] > 200 else \
                                    "red" if grid_analysis["grid_color"][0] > 200 and grid_analysis["grid_color"][1] < 50 else \
                                    "green" if grid_analysis["grid_color"][1] > 100 and grid_analysis["grid_color"][0] < 50 else \
                                    "colored"
                        color_info = f" with {color_name} grid lines"

                    enhanced_prompt += f"\n\nNote: This image appears to contain a grid-based graph{color_info}. " \
                                      f"Pay special attention to axis lines and any economic data plotted within the grid structure."

                # Create message with Gemini's expected format
                try:
                    response = await asyncio.wait_for(
                        asyncio.get_event_loop().run_in_executor(
                            None,
                            lambda: chat.send_message([
                                # First part is image attachment
                                file,
                                # Second part is text prompt
                                enhanced_prompt
                            ]).text
                        ),
                        timeout=60.0
                    )

                    print(f"Received response from Gemini API")

                except TimeoutError:
                    print("API request timed out")
                    return {
                        "success": False,
                        "error": "API request timed out",
                        "raw_response": None
                    }

                if not response:
                    return {
                        "success": False,
                        "error": "Empty response from API",
                        "raw_response": None
                    }

                # 経済グラフに関するキーワードの検出（より早い段階で実施）
                economics_keywords = ["supply", "demand", "price", "quantity", "market", "curve",
                                     "equilibrium", "intersection", "axis", "marginal", "elasticity",
                                     "economic graph", "economics diagram", "utility", "function"]

                economics_keyword_count = sum(1 for keyword in economics_keywords
                                            if keyword in response.lower())

                # テキスト計算問題を示すキーワード検出（追加：これがテキスト計算問題であることを示す）
                calculation_indicators = [
                    "calculation", "formula", "computed", "solve", "solving", "equation",
                    "working out", "computed", "calculating", "result is", "equals",
                    "steps", "step-by-step", "algebra", "computed as", "result of"
                ]

                calculation_count = sum(1 for indicator in calculation_indicators
                                      if indicator in response.lower())

                # 実際のグラフ描画の特徴（テキスト計算と区別するため）
                graph_drawing_indicators = [
                    "drawn graph", "visual representation", "plotted", "graph paper",
                    "coordinate system", "diagram shows", "graphical representation",
                    "depicted in the graph", "illustrated in the graph", "axes are labeled",
                    "the graph displays", "the graph illustrates", "visual diagram",
                    "the graph shows", "coordinate axes", "plotted against", "sloping",
                    "upward slope", "downward slope", "vertical axis", "horizontal axis"
                ]

                graph_drawing_count = sum(1 for indicator in graph_drawing_indicators
                                        if indicator in response.lower())

                # 追加：経済グラフの視覚的特徴を示す強力な指標
                econ_graph_visual_indicators = [
                    "curves intersect", "point of intersection", "equilibrium point",
                    "market equilibrium", "supply curve", "demand curve", "price axis",
                    "quantity axis", "upward sloping supply", "downward sloping demand",
                    "the intersection of supply and demand"
                ]

                econ_graph_visual_count = sum(1 for indicator in econ_graph_visual_indicators
                                           if indicator in response.lower())

                # 経済グラフの特徴がたくさんある場合は、グラフ描画カウントに加算（救済措置）
                graph_drawing_count += min(econ_graph_visual_count, 3)  # 最大3ポイントの救済加算

                # テキスト文書と計算が混在していると判断する条件（緩和）
                is_text_calculation = calculation_count >= 3 and graph_drawing_count <= 1

                # テキスト確率が高く、明らかな計算問題の特徴がある場合のみテキストと判断
                if text_scores['text_probability'] > 0.6 and is_text_calculation and economics_keyword_count < 3:
                    print(f"Detected text calculation problem (calc: {calculation_count}, drawing: {graph_drawing_count})")
                    return {
                        "success": False,
                        "error": "Image appears to be a text calculation problem rather than a drawn graph",
                        "text_probability": text_scores['text_probability'],
                        "calculation_indicators": calculation_count,
                        "raw_response": response
                    }

                # 経済グラフ特性が十分に検出された場合、NOT_A_GRAPHフラグを無視する
                # 修正: 視覚的描写の必要条件を緩和
                strong_economics_evidence = economics_keyword_count >= 4

                # APIがNOT_A_GRAPHと応答したが、経済グラフの特徴が十分に検出された場合
                if "NOT_A_GRAPH" in response.upper() and strong_economics_evidence:
                    print(f"API reported NOT_A_GRAPH but found {economics_keyword_count} economic keywords. Treating as economic graph.")
                    return {
                        "success": True,
                        "graph_type": "Economic Graph",
                        "axes": {
                            "x": "Horizontal axis (likely Quantity)",
                            "y": "Vertical axis (likely Price)"
                        },
                        "trends": ["Economic relationship detected in image"],
                        "points_of_interest": ["Economic concepts visualized"],
                        "economic_interpretation": "Economic diagram showing market relationships",
                        "quality_assessment": "Validated economic content despite API rejection",
                        "raw_response": response,
                        "text_probability": text_scores['text_probability'],
                        "economic_keywords": economics_keyword_count
                    }

                # 黒い画像など分析不可能な画像の判定（追加）
                # Gemini APIのフォールバックパターンをより広く検出
                api_fallback_patterns = [
                    # 既存のパターン
                    "unable to analyze", "completely black",
                    # 一般的なGemini APIの制限メッセージ
                    "i'm limited in what i can do", "falls outside of those limitations",
                    "outside my capabilities", "i'm not able to", "i can't process",
                    # 一般的なエラーフォールバックメッセージ
                    "i'm designed to be helpful", "is there something else i can try",
                    "something went wrong", "i cannot interpret", "failed to analyze",
                    # 画像解析の拒否パターン
                    "cannot analyze this image", "not able to analyze", "can't analyze this",
                    # エラー状態を示す短いレスポンス
                    "error occurred", "processing failed",
                    # その他のAPIフォールバックパターン
                    "i apologize", "unable to process", "i'm unable to", "formula", "Y =", "Y=", "function",
                    # 経済グラフがない場合のメッセージパターン（追加）
                    "no economics graph/diagram in the image", "does not contain any economics graph",
                    "no economic diagram", "doesn't show an economics graph",
                    "there is no economics graph", "there is no economic diagram",
                    "cannot identify any economics graph", "not an economics graph",
                    "image does not appear to contain an economic graph"
                ]

                if any(pattern in response.lower() for pattern in api_fallback_patterns):
                    print(f"Image cannot be analyzed: API returned a fallback or error response")
                    return {
                        "success": False,
                        "error": "Image cannot be analyzed (API returned generic response or error message)",
                        "raw_response": response
                    }

                # テキスト誤検出の検証 (修正)
                # Geminiがグラフではないと判断 かつ テキスト確率が高く、経済キーワードも少ない場合のみFalseと判定
                if "NOT_A_GRAPH" in response.upper() and text_scores['text_probability'] > 0.75 and economics_keyword_count < 3:
                    print(f"Confirmed text content detection: API confirms this is not a graph + text probability: {text_scores['text_probability']:.2f}")
                    return {
                        "success": False,
                        "error": "Image confirmed to be text content rather than graph",
                        "text_probability": text_scores['text_probability'],
                        "raw_response": response
                    }

                # グラフ認識の改善：NOT_A_GRAPHの判定を調整
                # グリッドパターンが検出されている、または明らかなグラフ特性があれば修正を適用
                if "NOT_A_GRAPH" in response.upper() and (
                   grid_analysis["has_grid"] or
                   (features['max_line_len'] > 40 and features['curve_count'] > 20 and text_scores['text_probability'] < 0.5)
                ):  # 修正: 条件を追加して改善
                    # Geminiの判断を無視して経済グラフとして解析
                    print("Gemini reported NOT_A_GRAPH but detected graph features. Treating as economic graph.")

                    # 基本的な経済グラフ情報を生成
                    return {
                        "success": True,
                        "graph_type": "Economic Diagram",
                        "axes": {
                            "x": "Horizontal axis",
                            "y": "Vertical axis"
                        },
                        "trends": ["Economic relationship shown in graph format"],
                        "points_of_interest": ["Graph representation of economic concepts"],
                        "economic_interpretation": "Economic diagram showing relationships between variables",
                        "quality_assessment": "Basic economic graph layout",
                        "raw_response": "AUTO-GENERATED: Economic graph detected despite Gemini API not recognizing it as graph."
                    }

                # Process response
                if "NOT_A_GRAPH" in response.upper():
                    return {
                        "success": False,
                        "error": "Image does not appear to be a graph or diagram",
                        "raw_response": response
                    }
                try:
                    # テキスト識別の信頼性チェック (修正: 経済グラフの特性を考慮)
                    response_lower = response.lower()
                    text_content_indicators = [
                        "this appears to be text", "this is text content",
                        "this is a document", "this is written text",
                        "marks", "Formula", "Function", "Y =",
                        "i cannot analyze this as a graph", "not a graph or chart"
                    ]

                    # 経済グラフ特有の特性を検出 (追加)
                    economic_graph_indicators = [
                        "supply", "demand", "curve", "equilibrium",
                        "price", "quantity", "axis", "intersection",
                        "market", "marginal", "elasticity", "cost",
                        "(P)", "(Q)"
                    ]

                    # 文書内で計算/説明の特徴を示すキーワード（追加）
                    document_explanation_indicators = [
                        "Economics", "questions", "calculation",
                        "formula", "Y =", "step", "problem", "calculated", "equation",
                        "solve", "answer", "result", "solution", "working"
                    ]

                    # 実際のグラフの視覚的特徴を示すキーワード（追加）
                    visual_graph_indicators = [
                        "the graph depicts", "the graph shows", "depicted in the graph",
                        "shown in the graph", "drawn", "plot", "plotted", "illustrated",
                        "visual diagram", "coordinate plane", "coordinate system",
                        "graphical representation"
                    ]

                    # 経済グラフの特徴を検出
                    economic_features = sum(1 for indicator in economic_graph_indicators if indicator in response_lower)
                    document_explanation_features = sum(1 for indicator in document_explanation_indicators if indicator in response_lower)
                    visual_graph_features = sum(1 for indicator in visual_graph_indicators if indicator in response_lower)

                    # 計算/説明文書と実際のグラフを区別するスコア（高いほどグラフの可能性）
                    graph_vs_document_score = visual_graph_features - document_explanation_features

                    # 視覚的なグラフの特徴が少なく、計算/説明の特徴が多い場合でも経済グラフの分析があれば成功と判断
                    if economic_features >= 3 and graph_vs_document_score < -2 and document_explanation_features >= 4:
                        # 経済グラフの分析が十分に含まれている場合は成功とする
                        graph_analysis_terms = ["graph", "axis", "curve", "plot", "diagram", "drawn", "visual"]
                        graph_analysis_count = sum(1 for term in graph_analysis_terms if term in response.lower())

                        if graph_analysis_count >= 2:
                            print(f"Economic text/calculation detected but graph analysis is present. Treating as success.")
                            return {
                                "success": True,
                                "graph_type": "Economic Graph with Calculations",
                                "axes": {
                                    "x": self._extract_axis_label(response, "x"),
                                    "y": self._extract_axis_label(response, "y")
                                },
                                "trends": ["Economic relationship with calculations"],
                                "points_of_interest": ["Graph with supporting calculations"],
                                "economic_interpretation": "Economic graph with detailed calculations",
                                "quality_assessment": "Mixed content with calculations and graph",
                                "raw_response": response,
                                "has_calculations": True  # 計算を含むことを示すフラグ
                            }
                        else:
                            print(f"Detected economic calculation/explanation text (graph vs doc score: {graph_vs_document_score})")
                            return {
                                "success": False,
                                "error": "Image appears to be economic text/calculations rather than visual graph",
                                "visual_graph_score": graph_vs_document_score,
                                "economic_terms": economic_features,
                                "explanation_terms": document_explanation_features,
                                "raw_response": response
                            }

                    # 経済グラフの特徴が強い場合の特別処理
                    strong_econ_graph_indicators = [
                        "supply and demand", "market equilibrium", "curves intersect",
                        "supply curve slopes", "demand curve slopes", "price axis",
                        "quantity axis", "point of intersection", "upward sloping",
                        "downward sloping", "equilibrium point", "equilibrium price"
                    ]

                    strong_econ_indicator_count = sum(1 for indicator in strong_econ_graph_indicators
                                                   if indicator in response_lower)

                    # 経済グラフの特徴が非常に強い場合は、他の判定を上書きして続行
                    if strong_econ_indicator_count >= 3:
                        print(f"Strong economic graph indicators detected ({strong_econ_indicator_count}). Continuing analysis.")
                        # ここで処理を続行（Falseを返さない）

                    # 以下のコードは保持
                    # テキスト指標があっても、視覚的グラフ特性が十分であれば処理継続
                    if any(indicator in response_lower for indicator in text_content_indicators):
                        if visual_graph_features >= 3 and graph_vs_document_score > 0:
                            # 視覚的グラフ特性が強い場合はグラフとして扱う
                            print(f"Text content detected but visual graph features are strong ({visual_graph_features} features). Treating as graph.")
                        else:
                            print(f"API identified image as text content in its analysis")
                            return {
                                "success": False,
                                "error": "API identified image as text rather than graph",
                                "raw_response": response
                            }

                    # 軸ラベルの生の情報を抽出（改善版）
                    x_axis_label = self._extract_axis_label(response, "x")
                    y_axis_label = self._extract_axis_label(response, "y")

                    # APIからの生の応答をログ出力
                    print(f"API detected X-axis: {x_axis_label}")
                    print(f"API detected Y-axis: {y_axis_label}")

                    # 経済グラフとしての妥当性検証 (追加)
                    graph_type = self._extract_info_from_response(response, "graph_type")
                    economic_indicators = ["supply", "demand", "cost", "price", "quantity",
                                          "revenue", "equilibrium", "market", "economic",
                                          "curve", "marginal", "utility", "indifference",
                                          "production", "function", "elasticity"]

                    economic_relevance = sum(1 for keyword in economic_indicators
                                           if keyword in response.lower())

                    # 経済グラフとしての特徴が一定数未満の場合、警告表示
                    if economic_relevance < 2:
                        print(f"Warning: Low economic relevance in graph analysis (score: {economic_relevance})")

                    # Extract structured information
                    result = {
                        "success": True,
                        "graph_type": graph_type,
                        "axes": {
                            # 軸ラベル情報をそのまま使用（デフォルト値を使わない）
                            "x": x_axis_label,
                            "y": y_axis_label
                        },
                        "trends": [],
                        "points_of_interest": [],
                        "economic_interpretation": self._extract_info_from_response(response, "economic"),
                        "quality_assessment": self._extract_info_from_response(response, "quality"),
                        "raw_response": response,
                        "text_probability": text_scores['text_probability'],  # テキスト確率を記録 (追加)
                        "economic_relevance_score": economic_relevance       # 経済関連性スコアを記録 (追加)
                    }

                    # Parse trends and points
                    trends = self._extract_info_from_response(response, "trends")
                    points = self._extract_info_from_response(response, "points")

                    if trends:
                        result["trends"] = [t.strip() for t in trends.split(',')]
                    else:
                        result["trends"] = ["Trend information not explicitly provided"]

                    if points:
                        result["points_of_interest"] = [p.strip() for p in points.split(',')]
                    else:
                        result["points_of_interest"] = ["Points of interest not explicitly provided"]

                    return result

                except Exception as e:
                    error_msg = str(e)
                    print(f"Error processing response: {error_msg}")

                    # Economic Graph Recognition Rescue - 特別な救済処理
                    # supply and demandグラフに関連するキーワードを検出
                    supply_demand_keywords = [
                        "supply curve", "demand curve", "equilibrium", "market equilibrium",
                        "curves intersect", "slopes upward", "slopes downward",
                        "price axis", "quantity axis", "point of intersection"
                    ]

                    # 供給と需要のグラフパターンを検出
                    supply_demand_count = sum(1 for kw in supply_demand_keywords
                                           if kw in response.lower())

                    # わずかな条件でも、供給と需要の重要な特徴があればグラフとして認識
                    if supply_demand_count >= 2 or ("supply" in response.lower() and "demand" in response.lower() and "equilibrium" in response.lower()):
                        print(f"Detected supply and demand graph features despite parsing error. Supply/demand indicators: {supply_demand_count}")
                        return {
                            "success": True,
                            "graph_type": "Supply and Demand Graph (Recovery)",
                            "axes": {
                                "x": "Quantity (Q)",
                                "y": "Price (P)"
                            },
                            "trends": ["Supply curve slopes upward", "Demand curve slopes downward", "Equilibrium at intersection"],
                            "points_of_interest": ["Market equilibrium"],
                            "economic_interpretation": "Standard supply and demand model showing market equilibrium",
                            "quality_assessment": "Economic graph recovered from parsing error",
                            "raw_response": response,
                            "error_recovery": True
                        }

                    # エラーメッセージからも経済グラフの特性を検出 (追加)
                    # 「no such group」などのエラーが出ても、経済グラフの特徴がレスポンスに含まれていれば救済処理を試みる
                    economics_terms = ["supply", "demand", "equilibrium", "price", "quantity", "market",
                                      "curve", "axis", "economic", "graph", "diagram"]

                    # 文書特性キーワード（テキスト文書と視覚的グラフを区別するため追加）
                    document_terms = ["calculation", "formula", "marks", "Function", "Y =", "step", "working", "problem",
                                     "economics", "answer", "solution", "equation"]

                    # 視覚的グラフ特性キーワード（追加）
                    visual_graph_terms = ["the graph shows", "depicted in the graph", "drawn",
                                         "plot", "coordinate", "visual representation"]

                    # 各特性の検出
                    economics_term_count = sum(1 for term in economics_terms
                                            if term in response.lower())
                    document_term_count = sum(1 for term in document_terms
                                           if term in response.lower())
                    visual_graph_term_count = sum(1 for term in visual_graph_terms
                                               if term in response.lower())

                    # 視覚的グラフと文書の区別スコア
                    graph_vs_doc_score = visual_graph_term_count - document_term_count

                    # エラーが発生しても、視覚的グラフと判断できる場合は回復処理
                    if economics_term_count >= 3 and "graph" in response.lower() and graph_vs_doc_score >= 0:
                        print(f"Error occurred but detected visual graph features. Treating as economic graph.")
                        return {
                            "success": True,
                            "graph_type": "Economic Graph (Error Recovery)",
                            "axes": {
                                "x": "Horizontal axis (extracted from error context)",
                                "y": "Vertical axis (extracted from error context)"
                            },
                            "trends": ["Economic relationship detected despite parsing error"],
                            "points_of_interest": ["Economic graph features extracted from error context"],
                            "economic_interpretation": "Economic diagram with market relationships",
                            "quality_assessment": "Recovered from parsing error",
                            "raw_response": response,
                            "text_probability": text_scores['text_probability'],
                            "error_recovery": True
                        }
                    # テキスト文書と判断された場合（より厳しい条件に変更）
                    elif economics_term_count >= 2 and document_term_count >= 3 and graph_vs_doc_score < -2:
                        print(f"Error occurred in parsing what appears to be economic text/calculation document.")
                        return {
                            "success": False,
                            "error": "Failed to process what appears to be economic text content",
                            "raw_response": response
                        }

                    return {
                        "success": False,
                        "error": f"Failed to process response: {error_msg}",
                        "raw_response": response
                    }
            except Exception as e:
                print(f"Error during API request: {str(e)}")
                return {
                    "success": False,
                    "error": f"API request failed: {str(e)}",
                    "raw_response": None
                }
        except Exception as e:
            print(f"Error in graph analysis: {str(e)}")
            return {
                "success": False,
                "error": f"Graph analysis failed: {str(e)}",
                "raw_response": None
            }

    def _extract_info_from_response(self, text: str, info_type: str) -> str:
        """Extract specific information from the response text."""
        import re

        patterns = {
            "graph_type": r"(?i)type.*?:\s*(.*?)(?:\n|$)",
            "x_axis": r"(?i)x[-\s]axis.*?:\s*(.*?)(?:\n|$)",
            "y_axis": r"(?i)y[-\s]axis.*?:\s*(.*?)(?:\n|$)",
            "trends": r"(?i)trends?.*?:\s*(.*?)(?:\n|$)",
            "points": r"(?i)(?:key\s*points|points\s*of\s*interest).*?:\s*(.*?)(?:\n|$)",
            "economic": r"(?i)economic.*?:\s*(.*?)(?:\n|$)",
            "quality": r"(?i)quality.*?:\s*(.*?)(?:\n|$)"
        }

        if info_type in patterns:
            match = re.search(patterns[info_type], text)
            if match:
                # 実際に見つかった軸ラベル情報や他の情報を返す
                extracted = match.group(1).strip()
                # "Unknown" や "Unlabeled" といった一般的なデフォルト値は返さない
                if extracted and not re.search(r'(?i)unknown|unlabeled|not\s+specified|no\s+label|unlabelled', extracted):
                    return extracted

        # より具体的なデフォルト値を返す
        if info_type == "trends" or info_type == "points":
            return ""
        elif info_type == "graph_type":
            return "Economic Diagram"
        elif info_type == "x_axis":
            return "Horizontal axis"
        elif info_type == "y_axis":
            return "Vertical axis"
        else:
            return ""  # その他のタイプのデフォルト値

    def _extract_axis_label(self, text: str, axis_type: str) -> str:
        """
        APIの応答からグラフの軸ラベルを厳密に抽出する
        学生が記載した実際のラベルのみを抽出し、デフォルト値の使用や推測を避ける

        Args:
            text: APIレスポンステキスト
            axis_type: 'x'または'y'を指定

        Returns:
            抽出された軸ラベル、または検出できない場合は「No labelling」を返す
        """
        import re

        # 軸タイプに基づく検索パターンを定義
        axis_name = "x" if axis_type == "x" else "y"
        axis_desc = "horizontal" if axis_type == "x" else "vertical"

        # 単一文字のラベルを検出するための特殊パターン(例: "labeled P", "labeled 'P'", "labeled (P)")
        single_char_patterns = [
            # パターン1: カッコ内の単一文字 "labeled (P)"
            rf"(?i){axis_name}[\s-]?axis.*?label(?:ed|s)?.*?\(([A-Za-z0-9])\)",
            rf"(?i){axis_desc}\s+axis.*?label(?:ed|s)?.*?\(([A-Za-z0-9])\)",

            # パターン2: 引用符内の単一文字 "labeled 'P'" または "labeled "P""
            rf'(?i){axis_name}[\s-]?axis.*?label(?:ed|s)?.*?[\'"]([A-Za-z0-9])[\'"]',
            rf'(?i){axis_desc}\s+axis.*?label(?:ed|s)?.*?[\'"]([A-Za-z0-9])[\'"]',

            # パターン3: そのまま表記された単一文字 "labeled P"
            rf"(?i){axis_name}[\s-]?axis.*?label(?:ed|s)?.*?\s([A-Za-z0-9])(?:\s|\.|,|\)|\(|$)",
            rf"(?i){axis_desc}\s+axis.*?label(?:ed|s)?.*?\s([A-Za-z0-9])(?:\s|\.|,|\)|\(|$)",

            # パターン4: 特殊パターン "The vertical axis (labeled "P") represents price."
            rf"(?i){axis_desc}\s+axis.*?\(label(?:ed|s)?.*?[\'\"]\s*([A-Za-z0-9])\s*[\'\"]\)",
            rf"(?i){axis_name}[\s-]?axis.*?\(label(?:ed|s)?.*?[\'\"]\s*([A-Za-z0-9])\s*[\'\"]\)"
        ]

        # まず単一文字のパターンで検索
        for pattern in single_char_patterns:
            match = re.search(pattern, text)
            if match:
                label = match.group(1).strip()
                if label and len(label) == 1:
                    print(f"検出された単一文字ラベル: {label}")
                    return label

        # 1. 厳密な軸ラベル検索パターン（学生が実際に書いたラベルを抽出）
        strict_patterns = [
            # 標準フォーマット「X-axis: Price」等
            rf"(?i){axis_name}[\s-]?axis\s*[:：]\s*([\w\s,/\(\)]+?)(?:\.|,|\n|$)",
            # 水平/垂直軸フォーマット「Horizontal axis: Quantity」等
            rf"(?i){axis_desc}\s+axis\s*[:：]\s*([\w\s,/\(\)]+?)(?:\.|,|\n|$)",
            # 軸ラベルが明示的に指定される場合「X-axis is labeled as Price」等
            rf"(?i){axis_name}[\s-]?axis\s+(?:is\s+)?label(?:ed|s)?\s+(?:as|with)?\s+([\w\s,/\(\)]+?)(?:\.|,|\n|$)",
            # 水平/垂直軸の場合「The horizontal axis represents Quantity」等
            rf"(?i){axis_desc}\s+(?:axis|variable).*?(?:represents|shows|displays|indicates)\s+([\w\s,/\(\)]+?)(?:\.|,|\n|$)"
        ]

        # すべての厳密なパターンで検索
        for pattern in strict_patterns:
            match = re.search(pattern, text)
            if match:
                label = match.group(1).strip()
                # 抽出されたラベルが有効か確認（空でなく、汎用的な記述でもない）
                if label and not re.search(r'(?i)unknown|unlabeled|not specified|no label', label):
                    # 学生が記載した実際のラベルを返す
                    return label

        # 2. より一般的なパターンの検索（軸とその内容の関連付け）
        general_patterns = [
            # 軸に関する一般的な記述
            rf"(?i){axis_name}[\s-]?axis.*?(represents|shows|displays|indicates|depicts|measures)\s+([\w\s,/]+?)(?:\.|,|\n|$)",
            rf"(?i){axis_desc}\s+(?:axis|variable).*?(?:represents|shows|displays|indicates|is)\s+([\w\s,/]+?)(?:\.|,|\n|$)",
            # 変数の直接参照
            rf"(?i)(?:the\s+)?{axis_name}[\s-]?axis\s+(?:variable|parameter)\s+is\s+([\w\s,/]+?)(?:\.|,|\n|$)",
        ]

        for pattern in general_patterns:
            match = re.search(pattern, text)
            if match:
                label = match.group(2).strip() if '(' in pattern else match.group(1).strip()
                if label and not re.search(r'(?i)unknown|unlabeled|not specified|no label', label):
                    return label

        # 3. 一般的なラベル抽出の別のアプローチ
        common_patterns = [
            rf"(?i){axis_name}[\s-]?axis[:：]?\s+(.*?)(?:\n|$)",
            rf"(?i){axis_desc}\s+axis[:：]?\s+(.*?)(?:\n|$)"
        ]

        for pattern in common_patterns:
            match = re.search(pattern, text)
            if match:
                label = match.group(1).strip()
                if label and not re.search(r'(?i)unknown|unlabeled|not specified|no label', label):
                    return label

        # ラベルが見つからない場合は「No labelling」を返す
        return "No labelling"

    def _enhanced_axis_extraction(self, text: str, axis_type: str) -> str:
        """
        APIの生のレスポンスからより詳細な軸情報を抽出する強化関数
        """
        import re

        # 軸タイプに基づく特定のキーワードとパターンを定義
        axis_name = "x" if axis_type == "x" else "y"
        axis_desc = "horizontal" if axis_type == "x" else "vertical"

        # 経済変数の一般的な名称リスト
        economic_vars = {
            "x": ["quantity", "output", "income", "consumption", "production", "gdp", "time", "goods"],
            "y": ["price", "cost", "revenue", "profit", "utility", "interest rate", "wage", "money"]
        }

        # 1. より多様なパターンで軸情報を検索
        patterns = [
            # 標準パターン
            rf"(?i){axis_name}[\s-]?axis\s*(?:is|shows|represents|indicates|depicts|measures)?\s*[:\s]\s*([\w\s,/]+?)(?:\.|,|\n|$)",
            # 軸の説明パターン
            rf"(?i){axis_desc} axis\s*(?:is|shows|represents|indicates|depicts|measures)?\s*[:\s]\s*([\w\s,/]+?)(?:\.|,|\n|$)",
            # 変数の直接参照
            rf"(?i)(?:the\s+)?{axis_name}[\s-]?axis\s+(?:variable|value|label|parameter)\s+is\s+([\w\s,/]+?)(?:\.|,|\n|$)",
            # 一般的な経済グラフの軸パターン
            rf"(?i){axis_desc}\s+(?:axis|variable).*?(?:represents|shows|displays|indicates|is)\s+([\w\s,/]+?)(?:\.|,|\n|$)"
        ]

        # すべてのパターンで検索
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                result = match.group(1).strip()
                # 一般的なデフォルト値や「unknown」を含む場合は無視
                if result and len(result) > 2 and not re.search(r'(?i)unknown|unlabeled|not specified|no label', result):
                    return result

        # 2. 一般的な経済変数のキーワード検索
        lowercase_text = text.lower()

        # 軸に関連する文脈を抽出
        axis_context_pattern = rf"(?i)(?:.*?{axis_name}[\s-]?axis|.*?{axis_desc} axis).*?(\n|$)"
        context_match = re.search(axis_context_pattern, lowercase_text)

        if context_match:
            context = context_match.group(0)

            # この軸タイプに関連する経済変数を検索
            for var in economic_vars[axis_type]:
                if var in context:
                    # 変数の周辺テキストを分析してより具体的な名称を抽出
                    var_pattern = rf"(?i)(?:the\s+)?({var}(?:[\s\w]*))(?:\s+is|\s+on|\s+as|\s+of|\s+that|\.|,|\n|$)"
                    var_match = re.search(var_pattern, context)
                    if var_match:
                        specific_var = var_match.group(1).strip()
                        return specific_var.capitalize()
                    return var.capitalize()

        # 3. グラフタイプに基づくデフォルト値
        graph_type_lower = text.lower()

        # 供給と需要のグラフ
        if "supply" in graph_type_lower and "demand" in graph_type_lower:
            return "Quantity" if axis_type == "x" else "Price"

        # 費用曲線
        elif "cost" in graph_type_lower and "curve" in graph_type_lower:
            return "Quantity" if axis_type == "x" else "Cost"

        # IS-LM モデル
        elif "is-lm" in graph_type_lower or ("is" in graph_type_lower and "lm" in graph_type_lower):
            return "Income/Output" if axis_type == "x" else "Interest Rate"

        # フィリップス曲線
        elif "phillips" in graph_type_lower:
            return "Unemployment" if axis_type == "x" else "Inflation"

        # 標準的なデフォルト値
        return "Quantity" if axis_type == "x" else "Price"

    def _evaluate_graph_properties(self, width, height, pixels, axis_scores, features, bg_analysis, params):
        """グラフ特性の総合評価（新機能）"""
        # 1. 基本的な特徴量の抽出
        curve_count = len(features['curves'])
        curve_density = curve_count / (width * height)
        point_count = len(features['points'])
        line_length = features['max_line_len']

        # 2. 軸スコアの評価
        h_score, v_score = axis_scores['h'], axis_scores['v']
        max_axis = max(h_score, v_score)
        min_axis = min(h_score, v_score)

        # 3. テキストらしさの判定
        is_text_like = (
            (curve_density > 0.01 and line_length < 6) or  # 高密度の短い線
            (curve_count > 150 and line_length <= 5) or    # 多数の短い線
            (point_count >= 10 and point_count / (width * height) > 0.0005)  # 多数の点
        )

        # 4. グラフパターンの評価
        # 4.1 軸ベースのグラフ判定
        has_strong_axis = max_axis >= 20 and min_axis > 0
        has_moderate_axis = 8 <= max_axis < 20 and min_axis > 0

        # 4.2 一般的なグラフ特徴
        valid_curve_range = params['min_curve_count'] <= curve_count <= params['max_curve_count']
        valid_curve_density = 0.00002 <= curve_density <= 0.015
        valid_line_length = line_length >= params['min_line_length']

        # 4.3 背景分析結果
        valid_background = bg_analysis['valid']
        content_ratio = bg_analysis['content_ratio']

        # 5. 総合スコアリングと判定ロジック
        # 5.1 テキスト判定で除外
        if is_text_like and not (has_strong_axis or valid_background):
            return False

        # 5.2 アスペクト比の極端なケースを除外
        aspect_ratio = width / height
        if aspect_ratio < 0.25 or aspect_ratio > 4.0:
            if not (has_strong_axis and valid_background):
                return False

        # 5.3 特徴ベース判定
        feature_based_graph = (
            valid_curve_range and valid_curve_density and
            ((has_strong_axis or has_moderate_axis) or valid_line_length) and
            not is_text_like
        )

        # 5.4 背景ベース判定
        background_based_graph = (
            valid_background and content_ratio >= 0.03 and
            curve_count >= 10 and not is_text_like
        )

        # 5.5 強い軸のみによる判定
        axis_based_graph = (
            has_strong_axis and curve_count <= 300 and
            not is_text_like
        )

        # 5.6 総合判定
        is_graph = (
            feature_based_graph or
            background_based_graph or
            axis_based_graph
        )

        # 詳細なデバッグ情報
        print(f"Graph evaluation:")
        print(f"- Feature based: {feature_based_graph}")
        print(f"- Background based: {background_based_graph}")
        print(f"- Axis based: {axis_based_graph}")
        print(f"- Is text-like: {is_text_like}")
        print(f"- Curve count: {curve_count}")
        print(f"- Max line length: {line_length}")
        print(f"- Axis scores: h={h_score}, v={v_score}")

        return is_graph

    def _is_potential_graph(self, image: PIL.Image.Image) -> bool:
        """画像がグラフの可能性があるかどうかを判定する"""
        try:
            # 画像のサイズと形状をチェック
            width, height = image.size

            if not self._validate_image_size(width, height):
                return False

            # グレースケールのピクセルデータを取得
            pixels = self._get_grayscale_pixels(image)

            # 分析用パラメータを初期化
            params = self._initialize_analysis_params(width, height)
            params['min_curve_count'] = 10
            params['max_curve_count'] = 2000
            params['min_line_length'] = 5

            # 背景領域分析（グラフ/テーブル/図の特徴を検出）
            bg_analysis = {
                'valid': self._detect_background_regions(image),
                'content_ratio': 0.2  # デフォルト値
            }

            # 軸の検出
            axis_scores = self._detect_axes(pixels, width, height)

            # グラフの特徴を検出
            features = self._detect_graph_features(pixels, width, height, params)

            # カラーグリッドパターンの検出（拡張機能）
            grid_analysis = self._detect_color_grid_patterns(image)

            # テキスト特性の分析 (追加)
            text_scores = self._detect_text_characteristics(pixels, width, height, features)

            # デバッグ情報を表示
            self._print_debug_info(axis_scores, features)
            print(f"Text detection scores: {text_scores}")

            # テキスト確率が高い場合は早期リターン (修正: 閾値を0.7から0.85に引き上げ)
            if text_scores['text_probability'] > 0.85:
                print(f"✗ High probability of being text content: {text_scores['text_probability']:.2f}")
                return False

            # 総合的なグラフ特性の評価
            if grid_analysis["has_grid"]:
                print(f"✓ Detected grid pattern with confidence {grid_analysis['grid_confidence']:.2f}")
                return True

            is_graph = self._evaluate_graph_properties(
                width, height, pixels, axis_scores, features, bg_analysis, params
            )

            if is_graph:
                print(f"✓ Image recognized as graph: {width}x{height}")
            else:
                print(f"✗ Not a graph: {width}x{height}")

            return is_graph

        except Exception as e:
            print(f"Error in graph detection: {str(e)}")
            return False

    def _detect_text_characteristics(self, pixels: list, width: int, height: int, features: dict) -> dict:
        """文字/テキストの特徴を検出し、テキストらしさのスコアを算出する"""
        result = {
            'text_probability': 0.0,
            'line_uniformity': 0.0,
            'character_like_patterns': 0,
            'text_line_patterns': 0
        }

        # 特徴がない場合は早期リターン
        if not features['curves'] and not features['points']:
            return result

        # 1. 均一な行パターンの検出（テキストの特徴）
        y_positions = [y for _, y in features['curves']]
        if not y_positions:
            return result

        # y座標をビンに分類してヒストグラムを作成
        bin_height = max(2, height // 40)  # 適応的なビンサイズ
        bins = {}
        for y in y_positions:
            bin_idx = y // bin_height
            if bin_idx in bins:
                bins[bin_idx] += 1
            else:
                bins[bin_idx] = 1

        # ビンの均一性を評価（テキスト行は均一に分布する傾向がある）
        non_empty_bins = [count for count in bins.values() if count > 3]
        if len(non_empty_bins) >= 3:
            # 標準偏差/平均の比率が小さいほど均一（テキストらしい）
            mean_count = sum(non_empty_bins) / len(non_empty_bins)
            if mean_count > 0:
                std_dev = (sum((c - mean_count) ** 2 for c in non_empty_bins) / len(non_empty_bins)) ** 0.5
                uniformity = 1.0 - min(1.0, std_dev / (mean_count + 1e-6))
                result['line_uniformity'] = uniformity

            # テキスト行パターンカウント
            if len(non_empty_bins) >= 5 and uniformity > 0.7:
                result['text_line_patterns'] = len(non_empty_bins)

        # 2. 文字に似た曲線パターンの検出
        char_patterns = 0
        char_heights = []
        processed = set()

        # 小さな接続曲線グループを検出（文字のような特性）
        for i, (x, y) in enumerate(features['curves']):
            if (x, y) in processed:
                continue

            # 小さな連結領域を検出
            cluster = [(x, y)]
            processed.add((x, y))
            points_to_check = [(x, y)]

            while points_to_check:
                cx, cy = points_to_check.pop(0)

                # 近傍点を探索
                for j, (nx, ny) in enumerate(features['curves']):
                    if (nx, ny) in processed:
                        continue

                    # 近い点を同じクラスタに
                    if ((nx - cx) ** 2 + (ny - cy) ** 2) <= 25:  # 5x5の範囲
                        cluster.append((nx, ny))
                        processed.add((nx, ny))
                        points_to_check.append((nx, ny))

            # 文字のようなサイズの塊（クラスタ）を検出
            if 5 <= len(cluster) <= 100:
                char_patterns += 1

                # クラスタの高さを計算
                if cluster:
                    min_y = min(y for _, y in cluster)
                    max_y = max(y for _, y in cluster)
                    height = max_y - min_y
                    if 8 <= height <= 50:  # 典型的な文字の高さ
                        char_heights.append(height)

        # 文字の高さの均一性（テキストでは文字高さが揃う）
        if len(char_heights) >= 3:
            avg_height = sum(char_heights) / len(char_heights)
            height_variance = sum((h - avg_height) ** 2 for h in char_heights) / len(char_heights)
            height_uniformity = 1.0 - min(1.0, (height_variance ** 0.5) / (avg_height + 1e-6))

            if height_uniformity > 0.85:  # 高さがとても均一（テキストらしい）
                result['character_like_patterns'] = char_patterns

        # 3. 総合的なテキスト確率の計算
        text_probability = 0.0

        # 行の均一性が高いほどテキストらしい
        if result['line_uniformity'] > 0.7:
            text_probability += result['line_uniformity'] * 0.4

        # 文字のようなパターンが多いほどテキストらしい
        if result['character_like_patterns'] >= 5:
            text_probability += min(1.0, result['character_like_patterns'] / 50) * 0.3

        # テキスト行パターンが多いほどテキストらしい
        if result['text_line_patterns'] >= 3:
            text_probability += min(1.0, result['text_line_patterns'] / 15) * 0.3

        # 軸や格子線がある場合はテキスト確率を下げる
        if features['max_line_len'] > 30:  # 長い線（軸など）がある
            text_probability *= 0.7

        result['text_probability'] = text_probability
        return result

    def _interpret_axis_label(self, graph_analysis: Dict, axis: str) -> str:
        """
        軸ラベル（X軸またはY軸）の解釈を行い、単一文字表記（'P'や'Q'など）も含めて
        適切な経済変数に変換する

        Args:
            graph_analysis: グラフ分析結果の辞書
            axis: 'x'または'y'を指定

        Returns:
            解釈された軸ラベル
        """
        # グラフ分析結果から軸データを取得
        if not isinstance(graph_analysis, dict):
            return "Unknown economic variable"

        axes = graph_analysis.get('axes', {})
        if not isinstance(axes, dict):
            return "Unknown economic variable"

        label = axes.get(axis, "").strip()
        raw_response = graph_analysis.get('raw_response', '').lower()

        # "No labelling"の場合は特別処理
        if label.lower() == "no labelling" or not label:
            # raw_responseからの抽出を試みる
            return self._extract_axis_from_raw_response(raw_response, axis)

        # 明示的な軸ラベルがある場合の処理
        # 単一文字の場合、拡張して返す
        single_char_mappings = {
            'p': "Price (P)",
            'q': "Quantity (Q)",
            'c': "Cost (C)",
            'r': "Revenue (R)",
            'y': "Income/Output (Y)",
            'i': "Interest Rate (i)",
            'l': "Labor (L)",
            'k': "Capital (K)",
            'w': "Wage (W)",
            's': "Supply (S)",
            'd': "Demand (D)"
        }

        # 単一文字かどうかチェック
        if len(label.strip()) == 1 and label.lower() in single_char_mappings:
            return single_char_mappings[label.lower()]
        else:
            # ラベルが既に意味のある言葉である場合はそのまま返す（最初の文字を大文字に）
            return label[0].upper() + label[1:] if len(label) > 1 else label.upper()

    def _extract_axis_from_raw_response(self, raw_response: str, axis: str) -> str:
        """
        APIレスポンステキストから軸ラベル情報を抽出する

        Args:
            raw_response: APIからの生のレスポンステキスト
            axis: 'x'または'y'を指定

        Returns:
            抽出された軸ラベル
        """
        # 軸固有のキーワードとパターンを定義
        axis_specific_info = {
            'x': {
                'patterns': [
                    r'x[\s-]?axis[\s:]*(is|shows|represents|displays|indicates|depicts)[\s:]*([\w\s]+)',
                    r'horizontal axis[\s:]*(is|shows|represents|displays|indicates|depicts)[\s:]*([\w\s]+)',
                    r'([\w\s]+)[\s-]on the x[\s-]?axis',
                    r'([\w\s]+)[\s-]on the horizontal axis',
                    r'x-axis:[\s]*([\w\s,]+)',
                    r'horizontal axis:[\s]*([\w\s,]+)'
                ],
                'keywords': ['quantity', 'output', 'income', 'production', 'consumption', 'time'],
                'single_chars': {'q': "Quantity", 'y': "Output/Income", 'x': "Quantity/Input"},
                'default': "Quantity (horizontal axis)"
            },
            'y': {
                'patterns': [
                    r'y[\s-]?axis[\s:]*(is|shows|represents|displays|indicates|depicts)[\s:]*([\w\s]+)',
                    r'vertical axis[\s:]*(is|shows|represents|displays|indicates|depicts)[\s:]*([\w\s]+)',
                    r'([\w\s]+)[\s-]on the y[\s-]?axis',
                    r'([\w\s]+)[\s-]on the vertical axis',
                    r'y-axis:[\s]*([\w\s,]+)',
                    r'vertical axis:[\s]*([\w\s,]+)'
                ],
                'keywords': ['price', 'cost', 'revenue', 'interest rate', 'wage', 'utility'],
                'single_chars': {'p': "Price", 'c': "Cost", 'r': "Revenue", 'i': "Interest Rate"},
                'default': "Price (vertical axis)"
            }
        }

        # 該当する軸の情報を取得
        axis_info = axis_specific_info[axis]

        # パターンマッチングを試行
        for pattern in axis_info['patterns']:
            match = re.search(pattern, raw_response, re.IGNORECASE)
            if match:
                # パターンによって抽出グループが異なる
                if 'is|shows|represents|displays|indicates|depicts' in pattern:
                    extracted = match.group(2)
                else:
                    # ':' が含まれるパターン
                    extracted = match.group(1) if match.lastindex == 1 else match.group(2)

                if extracted and len(extracted.strip()) > 1:  # 単一文字以上の場合のみ採用
                    # 余分な記号や空白を除去し、最初の文字を大文字に
                    cleaned = re.sub(r'[,:;]', '', extracted).strip()
                    return cleaned[0].upper() + cleaned[1:] if len(cleaned) > 1 else cleaned.upper()

        # キーワードベースの検索（軸に関連する特徴的な単語を探す）
        for keyword in axis_info['keywords']:
            if keyword in raw_response:
                # キーワードの周辺テキストをチェック（より具体的な表現を探す）
                context_pattern = rf'({keyword}[\s\w]{{0,20}})'
                matches = re.findall(context_pattern, raw_response)
                if matches:
                    best_match = max(matches, key=len).strip()
                    # 余分な記号や空白を除去し、最初の文字を大文字に
                    cleaned = re.sub(r'[,:;]', '', best_match).strip()
                    return cleaned[0].upper() + cleaned[1:] if len(cleaned) > 1 else cleaned.upper()
                return keyword[0].upper() + keyword[1:]  # キーワードをそのまま返す

        # 単一文字の検索
        for char, meaning in axis_info['single_chars'].items():
            if re.search(fr'\b{char}\b', raw_response, re.IGNORECASE):
                return meaning

        # デフォルト値を返す
        return axis_info['default']
    async def extract_text_from_image_async(self, image: PIL.Image.Image, context: str = "") -> Dict[str, Any]:
            """Extracts text from an image using Gemini's vision capabilities, focusing on handwritten text."""
            if not isinstance(image, PIL.Image.Image):
                return {"success": False, "error": "Invalid image object", "extracted_text": ""}

            # 画像が大きすぎる場合はリサイズを検討 (analyze_graphと同様のロジック)
            # 例:
            # MAX_IMAGE_DIMENSION = 2048
            # if image.width > MAX_IMAGE_DIMENSION or image.height > MAX_IMAGE_DIMENSION:
            #     image.thumbnail((MAX_IMAGE_DIMENSION, MAX_IMAGE_DIMENSION))

            ocr_prompt = """Please extract ALL text from this image.
Prioritize accuracy for handwritten text.
If there are distinct blocks of text that seem to correspond to questions and answers (e.g., "1a) [answer text]", "Question 2: [answer text]"), try to maintain that structure.
Return only the extracted text.
"""
            if context:
                ocr_prompt = f"{context}\n\n{ocr_prompt}"

            try:
                # PIL ImageをAPIが期待する形式に変換
                # analyze_graph内の画像準備ロジックを参考にする
                
                import tempfile
                import asyncio

                loop = asyncio.get_event_loop()

                def save_and_upload_image(img_to_save):
                    # delete=Falseにして、アップロード後に手動で削除する
                    with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_img_file:
                        if img_to_save.mode != 'RGB':
                            img_to_save = img_to_save.convert('RGB')
                        img_to_save.save(temp_img_file.name, format="JPEG", quality=95)
                        temp_img_path = temp_img_file.name
                    
                    uploaded_file = None
                    try:
                        # genai.upload_file は file_name ではなく path を期待する
                        uploaded_file = genai.upload_file(path=temp_img_path, mime_type="image/jpeg")
                        print(f"Successfully uploaded image to Gemini API: {uploaded_file.name}")
                    except Exception as upload_err:
                        print(f"Error uploading image to Gemini API: {str(upload_err)}")
                        # エラー発生時もファイルを削除する
                        if os.path.exists(temp_img_path):
                            os.remove(temp_img_path)
                        raise # エラーを再送出して呼び出し元で処理できるようにする
                    finally:
                        # 成功時も失敗時（アップロード前）もファイルを削除
                        if os.path.exists(temp_img_path):
                            os.remove(temp_img_path)
                    return uploaded_file
                
                uploaded_file_obj = await loop.run_in_executor(None, save_and_upload_image, image)

                if not uploaded_file_obj:
                     return {"success": False, "error": "Failed to prepare image for API (upload failed or returned None)", "extracted_text": ""}

                chat = self.model.start_chat()
                # Gemini Pro Visionは parts のリストとして [prompt, image_object] を期待
                # uploaded_file_obj は UploadedFile オブジェクトなのでそのまま渡せるはず
                response = await asyncio.to_thread(chat.send_message, [ocr_prompt, uploaded_file_obj])


                if response and hasattr(response, 'text') and response.text:
                    extracted_text = response.text.strip()
                    # 必要に応じて、ここでさらにテキストを整形する処理を追加できます
                    return {"success": True, "extracted_text": extracted_text}
                else:
                    error_message = "No text extracted or empty response"
                    if response and hasattr(response, 'prompt_feedback') and response.prompt_feedback:
                        error_message += f" (Prompt Feedback: {response.prompt_feedback})"
                    # アップロードされたが使われなかったファイルを削除する (Gemini APIの仕様による)
                    if uploaded_file_obj:
                        try:
                            await asyncio.to_thread(genai.delete_file, uploaded_file_obj.name)
                            print(f"Cleaned up unused uploaded file: {uploaded_file_obj.name}")
                        except Exception as delete_err:
                            print(f"Error deleting unused uploaded file {uploaded_file_obj.name}: {str(delete_err)}")
                    return {"success": False, "error": error_message, "extracted_text": ""}

            except Exception as e:
                error_str = str(e)
                # APIからの詳細なエラーメッセージが含まれているか確認
                if hasattr(e, 'response') and hasattr(e.response, 'text'):
                    error_str += f" | API Response: {e.response.text}"
                print(f"Error during OCR text extraction: {error_str}")
                # エラー時にもアップロードされたファイルを削除しようと試みる
                if 'uploaded_file_obj' in locals() and uploaded_file_obj:
                     try:
                        await asyncio.to_thread(genai.delete_file, uploaded_file_obj.name)
                        print(f"Cleaned up uploaded file on error: {uploaded_file_obj.name}")
                     except Exception as delete_err:
                        print(f"Error deleting uploaded file {uploaded_file_obj.name} on error: {str(delete_err)}")
                return {"success": False, "error": error_str, "extracted_text": ""}
