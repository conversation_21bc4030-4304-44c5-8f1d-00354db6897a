# Exam Marker

This project is an automated exam grading system for economics exams, leveraging Google's Gemini 2.5, specifically the gemini-2.5-flash-preview-04-17 model to analyze exam submissions in PDF format and automatically generate grades and feedback. It streamlines the grading process for economics exams in educational institutions, reducing faculty workload while providing consistent and detailed feedback.

## Overview

This system analyzes PDF exam submissions using the Google Gemini API, processes mathematical calculations, economic concepts, and provides comprehensive feedback based on predefined marking criteria. For consistent and reliable grading, it uses a temperature of 0.1 and top_p of 0.95. Advanced graph analysis capabilities for economic diagrams and plots are also implemented.

## Prerequisites

- Python 3.10+
- Google Gemini API Key (obtain from [Google AI Studio](https://aistudio.google.com/))
- PDF exam submissions in a directory

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd exam-marker

# Install dependencies using uv
uv pip install -e .
brew install tcl-tk

or

uv sync
pip install -e .
brew install tcl-tk
```

## Configuration

Create a `.env` file with the following structure:

```bash
# API Configuration
GEMINI_API_KEY=your-api-key-here

# Question Texts (customize these based on your exam content)
QUESTION_TEXT_1a="Find the equilibrium price and quantity..."

# Model Solutions (adjust to match your exam requirements)
SOLUTION_1a="For equilibrium: 130-5P = 10P-50..."

# Marking Criteria (format: marks,criterion|marks,criterion)
QUESTION_1a=3,calculation steps|2,correct result

# Graph Analysis Settings (customize for your economic diagrams)
GRAPH_ANALYSIS="You are an economics expert analyzing graphs..."

# Optional Settings
MARKER_NAME="Your Name"

Note: Customize these prompts and criteria to match your specific exam content,
economic concepts, and assessment requirements. The system will adapt its
analysis and grading based on these settings.
```

## Usage

### GUI Mode

Simply run the package:

```bash
python -m exam_marker

or

conda activate exam_marker
python -c "import tkinter"
conda install tk
/Users/<USER>/anaconda3/envs/exam_marker/bin/python -m src.exam_marker --gui

```

Follow the prompts to:
1. Select the directory containing PDF exam submissions
2. Choose where to save the marking results

### CLI Mode

```bash
python -m exam_marker /path/to/pdf/directory -o /path/to/output/directory -m "Marker Name"
```

### Output

For each exam submission, the system generates:
- Individual feedback file (`[candidate_number].md`)
- Combined feedback file (`combined_feedback.md`)

## Output Format

Individual feedback files include:
```markdown
# Exam Feedback
Candidate: [ID]
Total Marks: [total]

## Question 1a
Marks: [score]
[detailed feedback]
[criterion breakdown]

[Graph Analysis Results (if applicable)]
1. Graph Type:
   - [identified type]
2. Components:
   - X-axis: [description]
   - Y-axis: [description]
3. Key Features:
   - Trends: [identified trends]
   - Critical Points: [key points]
4. Economic Interpretation:
   [interpretation details]

[...other questions...]

Marked by: [marker name]
```

## Features

- Batch processing of multiple exam PDFs
- Comprehensive grading and feedback generation based on evaluation of both graphs and text answers
- Structured grading based on predefined criteria
- Progress tracking and error handling

## Project Structure

### Architecture Overview

```mermaid
sequenceDiagram
    participant User
    participant CLI/GUI
    participant ExamMarker
    participant PDFProcessor
    participant GraphAnalyzer
    participant GeminiAPI

    User->>CLI/GUI: Select exam PDF directory
    CLI/GUI->>ExamMarker: Initialize with API key

    loop Each PDF
        ExamMarker->>PDFProcessor: Open and validate PDF
        PDFProcessor-->>ExamMarker: Document object

        ExamMarker->>GraphAnalyzer: Start graph analysis
        GraphAnalyzer->>PDFProcessor: Extract images from PDF
        PDFProcessor-->>GraphAnalyzer: Image data
        GraphAnalyzer->>GeminiAPI: Send image for analysis
        Note over GeminiAPI: Using gemini-2.5-flash-preview-04-17
        GeminiAPI-->>GraphAnalyzer: Analysis results
        GraphAnalyzer-->>ExamMarker: Store graph data in cache

        loop Each Question
            ExamMarker->>PDFProcessor: Extract answer section
            PDFProcessor-->>ExamMarker: Answer text

            alt If graph required
                ExamMarker->>ExamMarker: Find matching graph from cache
                ExamMarker->>GeminiAPI: Send answer + graph for evaluation
                Note over GeminiAPI: Using gemini-2.5-flash-preview-04-17
                GeminiAPI-->>ExamMarker: Wait for rate limit (25s)
                GeminiAPI-->>ExamMarker: Evaluation result (JSON)
            else No graph required
                ExamMarker->>GeminiAPI: Send answer for evaluation
                Note over GeminiAPI: Using gemini-2.5-flash-preview-04-17
                GeminiAPI-->>ExamMarker: Wait for rate limit (25s)
                GeminiAPI-->>ExamMarker: Evaluation result (JSON)
            end

            ExamMarker->>ExamMarker: Format feedback
        end

        ExamMarker->>CLI/GUI: Save feedback files
    end

    CLI/GUI->>User: Display completion status
```

### Component Structure

- **__main__.py**: Application entry point
  - GUI and CLI interfaces
  - Environment settings and initialization
  - Overall processing flow control

- **marker.py**: Core grading system
  - Marking criteria management
  - Gemini API integration
  - Asynchronous processing
  - Statistics collection

- **graph_analyzer.py**: Graph analysis system
  - Image extraction from PDFs
  - Graph feature detection
  - Economic interpretation
  - Integration with marking process

- **section.py**: PDF processing and structuring
  - Hierarchical section management
  - Answer section identification
  - Text preprocessing

- **lang/**: Multilingual support
  - Language-specific processing
  - Language detection and selection
  - Localized messages

## Development

Run tests:
```bash
pytest tests/
```
PYTHONPATH=/Users/<USER>/Desktop/Exam-marker3 python tests/test_graph_extraction.py

## Relating paper

ChatGPT: Is It Reliable as an Automated Writing Evaluation Tool?

https://doi.org/10.18039/ajesi.1463503

## License

See LICENSE file for details.
