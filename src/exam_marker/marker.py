"""Core exam marking functionality"""
import os
import json
from pathlib import Path
import time
import asyncio
from typing import Dict, List, Tuple, Optional, Union
import fitz  # PyMuPDF
import PIL.Image
import google.generativeai as genai
from datetime import datetime
from functools import partial
from concurrent.futures import ThreadPoolExecutor
import re
from .graph_analyzer import GraphAnalyzer

class ExamMarker:
    def __init__(self, model: str, marker_name: str, gemini_api_key: str):
        """Initialize the ExamMarker with Gemini API credentials."""
        # 作業ディレクトリを明示的に設定
        self.working_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        print(f"Setting working directory to: {self.working_dir}")
        os.chdir(self.working_dir)
        print(f"Current working directory: {os.getcwd()}")

        genai.configure(api_key=gemini_api_key)
        generation_config = {
            "temperature": 0.1,
            "top_p": 0.95,
            "top_k": 40,
            "max_output_tokens": 8192,
            "response_mime_type": "text/plain",
        }
        self.model = genai.GenerativeModel(
            model_name="gemini-2.5-flash-preview-05-20",
            generation_config=generation_config
        )
        self.marker_name = marker_name
        self._load_criteria()
        self.statistics = {
            'total_exams': 0,
            'total_pages': 0,
            'processing_time': 0,
            'api_calls': 0,
            'graphs_analyzed': 0
        }
        self.last_api_call = 0
        self.MIN_API_INTERVAL = 25.0  # APIコール間隔を25秒に設定
        self.api_semaphore = asyncio.Semaphore(1)  # APIコールの同時実行を制限
        self.executor = ThreadPoolExecutor(max_workers=1)
        self.graph_analyzer = GraphAnalyzer(gemini_api_key)
        # グラフ分析結果を保存するための辞書を追加
        self.graph_cache = {}
        # 一時ファイル追跡用リスト
        self.temp_files = []
        # プログラム終了時のクリーンアップを登録
        import atexit
        atexit.register(self._cleanup_temp_files)

        # 一時ディレクトリの作成
        self.temp_dir = Path("temp_graphs")
        self.temp_dir.mkdir(exist_ok=True)

    def _load_criteria(self):
        """Load marking criteria and questions from environment variables"""
        self.questions = {}
        self.solutions = {}
        self.criteria = {}
        self.guidelines = []

        for key, value in os.environ.items():
            if key.startswith('MARKING_GUIDELINE_'):
                self.guidelines.append(value)
            elif key.startswith('QUESTION_TEXT_'):
                q_id = key.replace('QUESTION_TEXT_', '')
                self.questions[q_id] = value
            elif key.startswith('SOLUTION_'):
                q_id = key.replace('SOLUTION_', '')
                self.solutions[q_id] = value
            elif key.startswith('QUESTION_') and not key.startswith('QUESTION_TEXT_'):
                q_id = key.replace('QUESTION_', '')
                criteria_list = []
                for criterion in value.split('|'):
                    marks, desc = criterion.split(',', 1)
                    criteria_list.append({
                        'marks': int(marks),
                        'description': desc
                    })
                self.criteria[q_id] = criteria_list

    async def _wait_for_rate_limit(self):
        """Control API call intervals"""
        now = time.time()
        if self.last_api_call > 0:
            elapsed = now - self.last_api_call
            if elapsed < self.MIN_API_INTERVAL:
                await asyncio.sleep(self.MIN_API_INTERVAL - elapsed)
        self.last_api_call = time.time()

    async def _generate_content(self, contents):
        """Execute API calls asynchronously using ThreadPoolExecutor"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            partial(self.model.generate_content, contents)
        )

    async def evaluate_answer(self, question_id: str, answer_text: str, graph_analysis: Dict = None) -> Tuple[int, str]:
        """Evaluate a single answer using Gemini API"""
        print(f"\nEvaluating question {question_id}")

        # グラフ分析データの検証とデバッグ情報の表示
        if graph_analysis:
            if not isinstance(graph_analysis, dict):
                print(f"Warning: graph_analysis is not a dictionary (type: {type(graph_analysis)})")
                graph_analysis = None
            elif not graph_analysis.get('success', False):
                print(f"Warning: graph_analysis indicates analysis was not successful")
                graph_analysis = None
            elif not all(field in graph_analysis for field in ['graph_type', 'axes', 'trends', 'points_of_interest']):
                print(f"Warning: graph_analysis is missing required fields")
                graph_analysis = None
            else:
                print(f"Graph analysis provided: {graph_analysis.get('success', False)}")
                print(f"Graph type: {graph_analysis['graph_type']}")
                print(f"X-axis: {graph_analysis['axes']['x']}")
                print(f"Y-axis: {graph_analysis['axes']['y']}")

                # 追加のグラフ情報があるか確認
                if 'additional_graphs' in graph_analysis and isinstance(graph_analysis['additional_graphs'], list):
                    print(f"Additional graphs: {len(graph_analysis['additional_graphs'])}")
                    for idx, add_graph in enumerate(graph_analysis['additional_graphs']):
                        if isinstance(add_graph, dict) and add_graph.get('success', False):
                            print(f"  Additional graph {idx+1}: {add_graph['graph_type']}")
        else:
            print("No graph analysis provided")

        async with self.api_semaphore:  # APIコールの同時実行を制御
            await self._wait_for_rate_limit()

        total_marks = sum(c['marks'] for c in self.criteria[question_id])
        criteria_text = "\n".join([f"- {c['marks']} marks: {c['description']}" for c in self.criteria[question_id]])

        # Check if this question requires graph analysis based on marking criteria
        requires_graph = False
        for criterion in self.criteria[question_id]:
            if any(keyword in criterion['description'].lower() for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve']):
                requires_graph = True
                break

        # Prepare graph analysis text with better error handling
        graph_analysis_text = ""
        if graph_analysis and isinstance(graph_analysis, dict):
            if graph_analysis.get('success'):
                self.statistics['graphs_analyzed'] += 1
                print(f"Including graph analysis for question {question_id}")

                # 主要グラフの分析テキストを作成
                graph_analysis_text = f"""
GRAPH ANALYSIS RESULTS (Please consider this in your evaluation):
Graph/Diagram Technical Analysis:
1. Type: {self._determine_graph_type(graph_analysis)}
2. Components:
   - X-axis: {self._interpret_axis_label(graph_analysis, 'x')}
   - Y-axis: {self._interpret_axis_label(graph_analysis, 'y')}
3. Key Features:
   - Trends: {', '.join(graph_analysis.get('trends', ['Not specified']))}
   - Critical Points: {', '.join(graph_analysis.get('points_of_interest', ['Not specified']))}
Economic Analysis:
- Interpretation: {graph_analysis.get('economic_interpretation', 'Basic economic relationships are illustrated in this graph.')}
- Integration with Theory: The student has provided a visual representation that should be evaluated in conjunction with their written explanation.
"""

                # 追加のグラフがある場合、それらも分析に含める
                if 'additional_graphs' in graph_analysis and isinstance(graph_analysis['additional_graphs'], list):
                    additional_graphs = [g for g in graph_analysis['additional_graphs'] if isinstance(g, dict) and g.get('success', False)]
                    if additional_graphs:
                        graph_analysis_text += f"\nADDITIONAL GRAPHS ANALYSIS:\n"
                        for idx, add_graph in enumerate(additional_graphs):
                            graph_analysis_text += f"""
Graph {idx+1}:
- Type: {self._determine_graph_type(add_graph)}
- X-axis: {self._interpret_axis_label(add_graph, 'x')}
- Y-axis: {self._interpret_axis_label(add_graph, 'y')}
- Economic Interpretation: {add_graph.get('economic_interpretation', 'Shows economic relationships.')}
"""

                # 評価ガイドラインを追加
                graph_analysis_text += f"""
Evaluation Guidelines for Graph/Diagram:
1. Accuracy: Check if the graph correctly represents economic concepts
2. Clarity: Assess labeling, scaling, and overall presentation
3. Integration: Evaluate how well the graph supports the written explanation
4. Economic Understanding: Consider how effectively the graph demonstrates economic relationships
"""

                # 複数グラフの場合の追加ガイドライン
                if 'additional_graphs' in graph_analysis and isinstance(graph_analysis['additional_graphs'], list) and graph_analysis['additional_graphs']:
                    graph_analysis_text += f"""
5. Multiple Graphs Assessment:
   - Consider ALL graphs when evaluating the student's answer
   - Check if multiple graphs show different aspects of the economic concept
   - Evaluate how well the student integrates multiple graphical representations
"""
        elif requires_graph:
            graph_analysis_text = """
MISSING REQUIRED GRAPH/DIAGRAM ANALYSIS:

Critical Issue:
The student's answer lacks the required graph or diagram that is essential for this question.

Impact on Evaluation:
1. Visual Representation (0 points):
   - No graphical demonstration of economic concepts
   - Missing critical visual support for the arguments

2. Technical Analysis Impact:
   - Cannot evaluate axis labeling and scaling
   - Cannot assess accuracy of economic relationships
   - Missing visual representation of key points

3. Integration Issues:
   - Written explanation lacks necessary visual support
   - Economic concepts not reinforced through graphical representation
   - Incomplete demonstration of understanding

Evaluation Guidelines:
- Apply significant deductions for the missing visual component
- Consider any verbal descriptions of graphs/relationships in the text
- Evaluate remaining content with consideration of this major omission
"""

        prompt = f"""You are an economics examiner grading a specific answer for Question {question_id}.

STRICT INSTRUCTIONS:
- Consider both the written explanation AND the graph/diagram analysis when grading
- Focus ONLY on this specific question
- Do NOT refer to or summarize other questions
- Return ONLY a JSON object with all required fields
- Provide feedback in a detailed paragraph format, not bullet points
- Start your feedback with a comprehensive analysis of this specific answer
- MUST consider the graph analysis results when evaluating the answer

QUESTION {question_id}:
{self.questions[question_id]}

MODEL ANSWER:
{self.solutions[question_id]}

MARKING CRITERIA (Total {total_marks} marks):
{criteria_text}

MARKING GUIDELINES:
{chr(10).join('- ' + g for g in self.guidelines)}

OCR TEXT HANDLING:
If the student's answer below contains sections like [OCR_FULL_DOCUMENT_SCAN_START]...[OCR_FULL_DOCUMENT_SCAN_END]
or [OCR_TEXT_FROM_PAGE_X_START]...[OCR_TEXT_FROM_PAGE_X_END], these sections represent text extracted via Optical Character Recognition (OCR)
from potentially handwritten parts of the exam.
You MUST treat this OCR-extracted text as a valid part of the student's answer.
Actively search within these OCR sections for relevant information, responses, or elaborations for the current question ({question_id}).
Even if typed text is minimal or absent for this question, the OCR text might contain the complete answer.

STUDENT'S ANSWER:
'''{answer_text}'''

{graph_analysis_text if graph_analysis_text else ""}

GRAPH ANALYSIS INTEGRATION RULES:
1. If graph analysis is provided:
   - Evaluate how well the student's written explanation matches the graph
   - Check if the graph supports the student's arguments
   - Assess the accuracy of graph interpretation
2. If multiple graphs are provided:
   - Consider all provided graphs in your evaluation
   - Assess how well the student uses multiple graphs to illustrate different aspects
   - Give credit for comprehensive graphical analysis when appropriate
3. If graph is required but missing:
   - Apply significant deductions
   - Evaluate remaining content with consideration of this major omission

GRADING INSTRUCTIONS:
1. Strict Grading Rules:
   - Each marking criterion has a MAXIMUM point value that CANNOT be exceeded
   - Start from the maximum available points and ONLY subtract points for errors
   - Points awarded for each criterion MUST NOT exceed its specified maximum
   - The total marks awarded MUST NOT exceed the sum of all criteria maximums

2. Point Allocation Rules:
   - If a criterion is worth X points, you can only award 0 to X points (inclusive)
   - Partial points are allowed but must not exceed the maximum
   - Even exceptional answers cannot receive more than the maximum points
   - When in doubt, stay within the point limits

3. Marking Criteria Breakdown:
{criteria_text}

4. Evaluation Components:
   a) Written Component (Analysis and Explanation):
      - Theoretical understanding and accuracy
      - Use of economic terminology
      - Quality of explanations and arguments

   b) Visual Component (where required by marking criteria):
      - If marking criteria specifically requires graphs/diagrams:
        * Technical accuracy and presentation
        * Proper labeling and scaling
        * Economic relationships shown correctly

   c) Integration of Components:
      - Alignment between written and visual elements
      - Use of graph to support explanations
      - Overall coherence of the answer

5. Point Deduction Method:
   - Start with maximum points for each criterion
   - Deduct points for specific errors or omissions
   - Ensure deductions do not result in negative points
   - Double-check that no criterion exceeds its maximum points
2. For mathematical questions:
  - Check calculation steps and accuracy
  - Verify if formulas are correctly applied
  - Look for proper economic variable definitions
3. For theoretical questions:
  - Assess understanding of economic concepts
  - Check for proper terminology use
  - Evaluate quality of explanations
4. For graphical analysis:
  - If required graphs/diagrams are missing, apply significant deductions
  - For existing graphs/diagrams:
    * Check if correctly interpreted and labeled for X-axis and Y-axis
    * Verify if economic relationships are properly explained
    * Assess accuracy of trend analysis and key point identification

FEEDBACK FORMAT:
- Write feedback as a cohesive paragraph
- Focus on the strengths and weaknesses of THIS answer only
- Do NOT mention or summarize other questions
- Explain mark deductions clearly within the context of this question
- IMPORTANT: When mentioning points for criteria in the feedback text (both in the main 'feedback' field and in 'comment' fields within 'breakdown'), ensure these textual references to points DO NOT exceed the maximum points for that criterion. For example, do not write "(4/3 points)" if the criterion maximum is 3. All textual point references must be within the allowed range.


YOU MUST RETURN ONLY A VALID JSON OBJECT IN THIS EXACT FORMAT:
{{
    "marks": <integer between 0 and {total_marks}>,
    "feedback": "<detailed explanation for mark deductions>",
    "breakdown": [
        // Exactly {len(self.criteria[question_id])} items required
{chr(10).join(f'        // Criterion {i+1}: {c["description"]} (max {c["marks"]} points)' for i, c in enumerate(self.criteria[question_id]))}
        {{
            "marks": <number from 0 to criterion maximum>,
            "comment": "<explanation for marks awarded/deducted>"
        }}
        // IMPORTANT: Items MUST be in the same order as criteria above
    ],
    "analysis": {{
        "extracted_calculations": "<any mathematical calculations found>",
        "key_concepts": "<main economic concepts identified>",
        "errors": "<specific errors or omissions>"
    }}
}}

CRITICAL REQUIREMENTS:
1. JSON Structure:
   - ALL fields above are REQUIRED (marks, feedback, breakdown, analysis)
   - No text outside the JSON object
   - marks must be an integer and cannot exceed {total_marks}
   - feedback must be a non-empty string
   - analysis must have all three subfields

2. Breakdown Rules (STRICT):
   - MUST have {len(self.criteria[question_id])} items in order
   - Each item's marks MUST NOT exceed its maximum:
{chr(10).join(f"      - {c['description']}: max {c['marks']} points" for c in self.criteria[question_id])}
   - Sum of breakdown marks MUST equal total marks
   - Items MUST include both marks and comment fields
   - The 'comment' field for each breakdown item, if it mentions points, must only refer to points within the maximum for that specific criterion. Do not suggest points exceeding the maximum in the comment text.

RESPONSES VIOLATING THESE RULES WILL BE REJECTED"""
        max_retries = 2
        retry_count = 0

        while retry_count < max_retries:
            try:
                # Initialize chat history
                chat_history = []

                # Handle graph analysis and create proper chat history
                if graph_analysis and graph_analysis.get('success') and 'image' in graph_analysis:
                    try:
                        # すでに作成済みの一時ディレクトリを使用
                        # 一時ファイルパスを生成
                        temp_path = self.temp_dir / f"graph_{question_id}_{int(time.time())}.jpg"
                        graph_analysis['image'].save(temp_path, "JPEG")

                        # 追跡リストに追加
                        self.temp_files.append(temp_path)

                        # Upload image with retry logic
                        max_upload_retries = 3
                        upload_success = False

                        for attempt in range(max_upload_retries):
                            try:
                                # ファイルパスを文字列に変換して渡す
                                graph_file = genai.upload_file(
                                    str(temp_path),
                                    mime_type="image/jpeg"
                                )
                                upload_success = True
                                break
                            except Exception as e:
                                print(f"Warning: Graph upload attempt {attempt + 1} failed: {str(e)}")
                                if attempt < max_upload_retries - 1:
                                    await asyncio.sleep(2 ** attempt)  # Exponential backoff

                        if not upload_success:
                            raise RuntimeError("Failed to upload graph after multiple attempts")

                        # Create chat history with both text and image using Gemini's expected format
                        chat_history = [
                            {
                                "role": "user",
                                "parts": [
                                    graph_file,  # Pass the uploaded file object directly
                                    "This is a graph/diagram from the student's economics exam answer. "
                                    "Please analyze it considering economic concepts and relationships."
                                ]
                            }
                        ]

                        # もし追加グラフがある場合、それらも追加
                        if 'additional_graphs' in graph_analysis and isinstance(graph_analysis['additional_graphs'], list):
                            for idx, add_graph in enumerate(graph_analysis['additional_graphs']):
                                if isinstance(add_graph, dict) and add_graph.get('success', False) and 'image' in add_graph:
                                    try:
                                        # Save additional image
                                        add_temp_path = self.temp_dir / f"graph_{question_id}_additional_{idx}_{int(time.time())}.jpg"
                                        add_graph['image'].save(add_temp_path, "JPEG")

                                        # Add to tracking list
                                        self.temp_files.append(add_temp_path)

                                        # Upload additional image
                                        for attempt in range(max_upload_retries):
                                            try:
                                                add_graph_file = genai.upload_file(
                                                    str(add_temp_path),
                                                    mime_type="image/jpeg"
                                                )
                                                # Add to chat history
                                                chat_history.append(
                                                    {
                                                        "role": "user",
                                                        "parts": [
                                                            add_graph_file,
                                                            f"This is additional graph {idx+1} from the student's economics exam answer. "
                                                            "Please consider this in your analysis as well."
                                                        ]
                                                    }
                                                )
                                                break
                                            except Exception as e:
                                                print(f"Warning: Additional graph upload attempt {attempt + 1} failed: {str(e)}")
                                                if attempt < max_upload_retries - 1:
                                                    await asyncio.sleep(2 ** attempt)

                                        # 一時ファイルのクリーンアップは _cleanup_temp_files で一括処理するため不要
                                        # ファイル追跡リストに追加済み
                                        pass

                                    except Exception as e:
                                        print(f"Warning: Failed to process additional graph image {idx}: {str(e)}")

                        # 一時ファイルのクリーンアップは _cleanup_temp_files で一括処理するため不要
                        # ファイル追跡リストに追加済み
                        pass
                    except Exception as e:
                        print(f"Warning: Failed to process graph image: {str(e)}")
                        # Fallback to text-only analysis
                        chat_history = []

                # Initialize chat session with history
                chat = self.model.start_chat(history=chat_history)

                # Send evaluation request
                print(f"Sending evaluation request for question {question_id}")
                response = chat.send_message(prompt)
                self.statistics['api_calls'] += 1
                print("Response received from Gemini API")

                response_text = response.text.strip()

                try:
                    result = json.loads(response_text)
                except json.JSONDecodeError:
                    start = response_text.find('{')
                    end = response_text.rfind('}')
                    if start != -1 and end != -1:
                        json_str = response_text[start:end+1]
                        result = json.loads(json_str)
                    else:
                        raise ValueError("Invalid JSON format in response")

                # Immediately clamp marks in result['breakdown'] from Gemini
                if 'breakdown' in result and isinstance(result['breakdown'], list):
                    clamped_breakdown_for_validation = []
                    for i, item_from_gemini in enumerate(result['breakdown']):
                        if isinstance(item_from_gemini, dict) and 'marks' in item_from_gemini and \
                           i < len(self.criteria[question_id]):
                            criterion_max_marks = self.criteria[question_id][i]['marks']
                            # Clamp the marks from Gemini
                            clamped_marks = min(item_from_gemini.get('marks', 0), criterion_max_marks)
                            clamped_item = item_from_gemini.copy() # Avoid modifying original dict directly if not intended
                            clamped_item['marks'] = clamped_marks
                            clamped_breakdown_for_validation.append(clamped_item)
                            print(f"DEBUG: Q{question_id} Criterion {i+1}: Original={item_from_gemini.get('marks', 0)}, Clamped={clamped_marks}, Max={criterion_max_marks}")
                        else:
                            # If item is not as expected or no corresponding criterion, add as is or handle error
                            clamped_breakdown_for_validation.append(item_from_gemini)
                    # Replace original breakdown with the one where marks are clamped
                    # This ensures that validate_breakdown and subsequent logic use clamped marks
                    result['breakdown'] = clamped_breakdown_for_validation

                # Function to validate breakdown items (now using potentially pre-clamped items)
                def validate_breakdown(items):
                    if not items or len(items) != len(self.criteria[question_id]):
                        raise ValueError("Incorrect number of breakdown items")

                    total = 0
                    for item, crit in zip(items, self.criteria[question_id]):
                        if not isinstance(item, dict) or 'marks' not in item or 'comment' not in item:
                            raise ValueError("Invalid breakdown item format")
                        if not isinstance(item['marks'], (int, float)) or item['marks'] < 0:
                            raise ValueError("Invalid marks in breakdown item")
                        if item['marks'] > crit['marks']:
                            raise ValueError(f"Breakdown item marks ({item['marks']}) exceeds maximum ({crit['marks']})")
                        total += item['marks']
                    return total

                # Support test fixtures schema with 'marks_awarded' and 'breakdown'
                if 'marks_awarded' in result and 'breakdown' in result:
                    marks = result.get('marks_awarded', 0)
                    feedback = result.get('feedback', "")
                    breakdown_items = result.get('breakdown', [])
                    analysis = result.get('analysis', {})

                    # Validate breakdown items (which are now pre-clamped) and check if total matches
                    # Note: breakdown_items here refers to result['breakdown'] which was modified above
                    total_awarded = validate_breakdown(result['breakdown'])
                    print(f"DEBUG: Q{question_id} Original marks_awarded={marks}, Breakdown total={total_awarded}")
                    # Update the main 'marks' to be the sum of the (now clamped) breakdown marks
                    marks = total_awarded
                    result['marks'] = total_awarded # Ensure result dict is also updated

                    # No need to check total_awarded != marks if marks is now derived from total_awarded
                    # However, if 'marks_awarded' was the original field, you might want to log a discrepancy
                    # if result.get('marks_awarded', 0) != total_awarded:
                    #     print(f"Warning: Original 'marks_awarded' ({result.get('marks_awarded',0)}) "
                    #           f"differs from sum of clamped breakdown ({total_awarded}) for Q{question_id}")

                    # Assign the (now clamped) breakdown_items for further use
                    breakdown_items = result['breakdown']
                else:
                    if not all(k in result for k in ['marks', 'feedback', 'analysis']):
                        raise ValueError("Missing required fields in response")
                    if not isinstance(result['analysis'], dict) or not all(k in result['analysis'] for k in ['extracted_calculations', 'key_concepts', 'errors']):
                        raise ValueError("Invalid analysis structure in response")
                    marks = result['marks']
                    feedback = result['feedback']
                    analysis = result['analysis']
                    breakdown_items = []

                    # For standard schema, validate breakdown if present
                    if 'breakdown' in result and isinstance(result['breakdown'], list):
                        total_awarded = validate_breakdown(result['breakdown'])
                        print(f"DEBUG: Q{question_id} Standard schema - Original marks={marks}, Breakdown total={total_awarded}")
                        # CRITICAL: Update marks to match breakdown total for consistency
                        marks = total_awarded
                        result['marks'] = total_awarded
                        breakdown_items = result['breakdown']

                if not isinstance(marks, (int, float)) or marks < 0 or marks > total_marks:
                    raise ValueError(f"Invalid marks value: {marks}")
                if not isinstance(feedback, str) or len(feedback.strip()) == 0:
                    raise ValueError("Invalid feedback")

                # 計算結果はGemini APIの採点用にのみ使用し、フィードバックには含めない
                detailed_feedback = f"{feedback}"
                # エラー情報は feedback に含まれているため、別途出力しない

                # Marking criteriaごとの配点をフィードバック文章末尾に挿入
                inserted = False
                if breakdown_items and len(breakdown_items) == len(self.criteria[question_id]):
                    # まず各criteriaの得点が配点を超えていないかチェック・修正
                    normalized_breakdown = []
                    total_awarded = 0
                    for crit_entry, br in zip(self.criteria[question_id], breakdown_items):
                        max_pts = crit_entry.get("marks", 0)
                        # 得点が配点を超えている場合は配点に制限
                        awarded = min(br.get("marks", 0), max_pts)
                        total_awarded += awarded
                        normalized_breakdown.append({
                            "marks": awarded,
                            "comment": br.get("comment", "").strip()
                        })

                    print(f"DEBUG: Q{question_id} Final normalized total={total_awarded}, Original marks={marks}")
                    # CRITICAL: Ensure final marks matches normalized breakdown total
                    marks = total_awarded

                    # 各criteriaの配点を文章内または末尾に挿入
                    for crit_entry, br in zip(self.criteria[question_id], normalized_breakdown):
                        max_pts = crit_entry.get("marks", 0)
                        awarded = br.get("marks", 0)
                        comment_text = br.get("comment", "").strip()
                        score_text = f" ({awarded}/{max_pts} points)"

                        # comment_textがfeedback内に存在し、かつ空でない場合のみ処理
                        if comment_text and comment_text in detailed_feedback:
                            pattern = re.escape(comment_text) + r'([.!?])?(\s*|$)'
                            match = re.search(pattern, detailed_feedback)

                            if match:
                                punctuation_start = match.start(1) if match.group(1) else match.end(0)
                                detailed_feedback = detailed_feedback[:punctuation_start] + score_text + detailed_feedback[punctuation_start:]
                                inserted = True

                    # breakdownがあるがcomment_textがfeedbackに見つからない場合は、末尾にcriteriaごとの配点を列挙
                    if not inserted:
                        detailed_feedback += "\n\n[Marking breakdown]\n"
                        for crit_entry, br in zip(self.criteria[question_id], normalized_breakdown):
                            max_pts = crit_entry.get("marks", 0)
                            awarded = br.get("marks", 0)
                            desc = crit_entry.get("description", "")
                            detailed_feedback += f"- {desc} ({awarded}/{max_pts} points)\n"
                else:
                    # breakdownがない場合は合計得点をcriteria配点比率で自動配分し、(配分点/満点 points)で列挙
                    detailed_feedback += "\n\n[Marking breakdown]\n"
                    total_marks = sum(c.get("marks", 0) for c in self.criteria[question_id])
                    # marks変数はAPIレスポンスから取得済み
                    remaining = marks
                    allocated = []
                    # 各criteriaに整数で配点を割り振る（端数は最後にまとめて調整）
                    for i, crit_entry in enumerate(self.criteria[question_id]):
                        max_pts = crit_entry.get("marks", 0)
                        desc = crit_entry.get("description", "")
                        if i < len(self.criteria[question_id]) - 1:
                            # 小数点以下切り捨てで暫定配分
                            alloc = int(marks * max_pts / total_marks)
                            allocated.append(alloc)
                            remaining -= alloc
                        else:
                            # 端数を最後のcriteriaにまとめて配分
                            alloc = max(0, remaining)
                            allocated.append(alloc)
                    for crit_entry, alloc_value in zip(self.criteria[question_id], allocated):
                        max_pts = crit_entry.get("marks", 0)
                        desc = crit_entry.get("description", "")
                        # Ensure the allocated value for display does not exceed the criterion's max points
                        display_alloc = min(alloc_value, max_pts)
                        detailed_feedback += f"- {desc} ({display_alloc}/{max_pts} points)\n"

                await asyncio.sleep(3)

                return int(marks), detailed_feedback

            except Exception as e:
                retry_count += 1
                print(f"API Error for question {question_id} (attempt {retry_count}/{max_retries}): {str(e)}")

                if "429" in str(e):
                    wait_time = min(5 * retry_count + 1, 15)
                    await asyncio.sleep(wait_time)
                elif retry_count == max_retries:
                    return 0, f"Error in grading: {str(e)}"
                else:
                    await asyncio.sleep(2 * retry_count)

    def _extract_json_from_response(self, response) -> str:
        """Extract and validate JSON from API response"""
        response_text = response.text.strip()

        try:
            result = json.loads(response_text)
        except json.JSONDecodeError as e:
            print(f"JSON parsing error: {str(e)}")
            print(f"Raw response: {response_text}")
            raise ValueError(f"Invalid JSON format: {str(e)}")

        if not all(k in result for k in ['marks', 'feedback', 'analysis']):
            raise ValueError("Missing required fields in response")

        if not isinstance(result['analysis'], dict) or not all(k in result['analysis'] for k in ['extracted_calculations', 'key_concepts', 'errors']):
            raise ValueError("Invalid analysis structure in response")

        return response_text

    async def _find_answer_section(self, doc, q_id: str) -> str:
        """Enhanced answer extraction with flexible question number matching and graph association"""
        try:
            # Extract text using consistent method across all text extraction points
            full_text = ""
            for page_num, page in enumerate(doc):
                try:
                    text = page.get_text("text", flags=fitz.TEXT_PRESERVE_LIGATURES | fitz.TEXT_PRESERVE_WHITESPACE)
                    full_text += f"=== Page {page_num + 1} ===\n{text}\n\n"
                except Exception as e:
                    print(f"Warning: Text extraction failed for page: {str(e)}")
                    continue

            question_text = self.questions.get(q_id, "").strip()

            # Create flexible patterns for question number matching
            base_q_num = q_id[:-1]  # e.g. "1" from "1a"
            sub_q_char = q_id[-1]  # e.g. "a" from "1a"

            # Enhanced patterns with more flexible matching
            patterns = [
                # Pattern 1: Standard format (1a, 1A)
                rf'{re.escape(q_id)}\s*[:：]?\s*(?P<answer>.*?)(?=\n\s*\d+[a-zA-Z]\s*[:：]|$)',
                # Pattern 2: Spaced format (1 a), 1 A))
                rf'{base_q_num}\s+{sub_q_char}\)\s*[:：]?\s*(?P<answer>.*?)(?=\n\s*\d+\s+[a-zA-Z]\)\s*[:：]|$)',
                # Pattern 3: Sub-question only (a), A))
                rf'{sub_q_char}\)\s*[:：]?\s*(?P<answer>.*?)(?=\n\s*[a-zA-Z]\s*[:：]|$)',
                # Pattern 4: Sub-question only with colon (a:, A:)
                rf'{sub_q_char}\s*[:：]\s*(?P<answer>.*?)(?=\n\s*[a-zA-Z]\s*[:：]|$)',
                # Pattern 5: Question text match
                rf'{re.escape(question_text)}\s*(?P<answer>.*?)(?=\n\s*\d+[a-zA-Z]\s*[:：]|$)',
                # New pattern: Question number in brackets [1a]
                rf'\[{re.escape(q_id)}\]\s*(?P<answer>.*?)(?=\n\s*\[\d+[a-zA-Z]\]|$)',
                # New pattern: Question number with dot 1.a
                rf'{base_q_num}\.{sub_q_char}\s*(?P<answer>.*?)(?=\n\s*\d+\.[a-zA-Z]|$)',
                # New pattern: Flexible number matching (1a, 1 a, 1.a, 1-a)
                rf'{base_q_num}[\s\.\-]?{sub_q_char}\s*[:：]?\s*(?P<answer>.*?)(?=\n\s*\d+[\s\.\-]?[a-zA-Z]\s*[:：]|$)',
                # New pattern: Question text with partial match
                rf'{re.escape(question_text[:20])}.*?\s*(?P<answer>.*?)(?=\n\s*\d+[a-zA-Z]\s*[:：]|$)',
                # 追加パターン1: 数字+スペース+大文字+閉じカッコ (1 A), 1 B))
                rf'{base_q_num}\s+{sub_q_char.upper()}\)\s*[:：]?\s*(?P<answer>.*?)(?=\n\s*\d+\s+[A-Z]\)\s*[:：]|$)',
                # 追加パターン2: 独立した大文字+閉じカッコ (A), B))
                rf'{sub_q_char.upper()}\)\s*[:：]?\s*(?P<answer>.*?)(?=\n\s*[A-Z]\)\s*[:：]|$)',
                # 追加パターン3: 数字と大文字の連結形式 (1A), 1B))
                rf'{base_q_num}{sub_q_char.upper()}\)\s*[:：]?\s*(?P<answer>.*?)(?=\n\s*\d+[A-Z]\)\s*[:：]|$)',
                # 追加パターン4: 数字+スペース+大文字（閉じカッコなし） (1 A, 1 B)
                rf'{base_q_num}\s+{sub_q_char.upper()}\s*[:：]?\s*(?P<answer>.*?)(?=\n\s*\d+\s+[A-Z]\s*[:：]|$)',
                # 追加パターン5: 独立した大文字のみ (A., B.)
                rf'{sub_q_char.upper()}\.\s*(?P<answer>.*?)(?=\n\s*[A-Z]\.\s*|$)'
            ]

            # Try matching with all patterns
            for pattern in patterns:
                match = re.search(pattern, full_text, flags=re.DOTALL | re.IGNORECASE)
                if match and match.group("answer").strip():
                    print(f"Found answer for {q_id} using pattern: {pattern}")
                    return match.group("answer").strip()

            # If no match found, try Gemini-based extraction
            print(f"Could not locate question {q_id} using regex, trying Gemini...")
            return await self._extract_answer_via_gemini(full_text, q_id)

        except Exception as e:
            print(f"Error finding answer section for question {q_id}: {str(e)}")
            return None

    async def _extract_answer_via_gemini(self, full_text: str, q_id: str) -> str:
        """Use Gemini API to extract the student's answer section"""
        question_text = self.questions.get(q_id, "")
        prompt = f"""You are a text extraction assistant. Extract the student's answer for a specific exam question.

EXAM SUBMISSION:
'''{full_text}'''

TARGET QUESTION:
Question {q_id}: {question_text}

EXTRACTION RULES:
1. Exclude question number ({q_id}) and question text from the answer
2. Extract up to the start of the next question
3. Include all bullet points and calculations
4. Return "Answer section not found for question {q_id}" if no answer is found

Return ONLY a JSON object in this format:
{{
    "answer": "<extracted answer text>"
}}"""

        response = await self._generate_content(prompt)
        response_text = response.text.strip()

        try:
            result = json.loads(response_text)
            answer = result.get("answer", "").strip()
            if not answer:
                answer = f"Answer section not found for question {q_id}"
            return answer
        except json.JSONDecodeError:
            return f"Answer section not found for question {q_id}"

    async def _analyze_and_cache_graphs(self, doc, pdf_path: str) -> None:
        """Analyze graphs from PDF and cache results"""
        print("\n=== Step 1: Graph Analysis ===")
        print("Starting sequential graph extraction and analysis...")

        total_pages = len(doc)
        self.graph_cache = {}  # キャッシュをリセット

        for page_num in range(total_pages):
            print(f"\nAnalyzing page {page_num + 1}/{total_pages}...")

            try:
                # グラフ画像の抽出と前処理
                images = await self.graph_analyzer.extract_images_from_pdf(pdf_path, page_num)
                if not images:
                    print(f"✗ No graphs found on page {page_num + 1}")
                    continue

                # 各画像の分析
                analysis_results = []
                for img_idx, img in enumerate(images):
                    print(f"  Analyzing graph {img_idx + 1}/{len(images)}...")

                    try:
                        # 画像をRGBモードに変換
                        if img.mode != 'RGB':
                            img = img.convert('RGB')

                        # PIL.Image.Imageオブジェクトを直接渡す（バイト列ではなく）
                        analysis = await self.graph_analyzer.analyze_graph(img)

                        if analysis.get('success'):
                            analysis['image'] = img  # 元画像を保存
                            analysis_results.append(analysis)
                            print(f"  ✓ Successfully analyzed graph {img_idx + 1}")
                        else:
                            print(f"  ✗ Graph analysis failed: {analysis.get('error')}")

                    except Exception as e:
                        print(f"  Warning: Graph analysis error: {str(e)}")
                        continue

                if analysis_results:
                    self.graph_cache[page_num] = analysis_results
                    print(f"✓ Found {len(analysis_results)} valid graphs on page {page_num + 1}")
                else:
                    print(f"✗ No valid graphs found on page {page_num + 1}")

            except Exception as e:
                print(f"Error analyzing page {page_num + 1}: {str(e)}")
                continue

        print("\nGraph analysis completed. Summary:")
        cache_status = {k: len(v) for k, v in self.graph_cache.items() if isinstance(k, int)}
        print(f"- Pages with graphs: {len(cache_status)}")
        print(f"- Graphs per page: {cache_status}")

    async def process_pdf(self, pdf_path: str) -> str:
        """Process a single PDF exam submission and return formatted feedback"""
        doc = None
        feedback_sections = []
        total_marks = 0
        start_time = time.time()
        try:
            print(f"\n=== Starting PDF Processing ===")
            print(f"PDF Path: {pdf_path}")
            print(f"Current working directory: {os.getcwd()}")
            print(f"Absolute PDF path: {os.path.abspath(pdf_path)}")

            doc = fitz.open(pdf_path)
            self.statistics['total_pages'] += len(doc)
            print(f"Total pages in PDF: {len(doc)}")

            all_pages_ocr_texts: Dict[int, str] = {}
            print("\n--- Starting Full Document OCR ---")
            if doc:
                for page_num_ocr in range(len(doc)):
                    page_for_ocr = doc[page_num_ocr]
                    try:
                        print(f"Performing OCR on page {page_num_ocr + 1}/{len(doc)}...")
                        # Use a moderate resolution for full page OCR to balance speed and accuracy
                        pix_ocr = page_for_ocr.get_pixmap(matrix=fitz.Matrix(2.0, 2.0), alpha=False)
                        img_pil_ocr = PIL.Image.frombytes("RGB", [pix_ocr.width, pix_ocr.height], pix_ocr.samples)

                        ocr_result = await self.graph_analyzer.extract_text_from_image_async(img_pil_ocr)

                        if ocr_result and ocr_result.get("success"):
                            extracted_text = ocr_result.get("extracted_text", "")
                            if extracted_text.strip():
                                all_pages_ocr_texts[page_num_ocr] = extracted_text.strip()
                                print(f"  Page {page_num_ocr + 1} OCR successful (first 50 chars): {extracted_text.strip()[:50]}...")
                            else:
                                print(f"  Page {page_num_ocr + 1} OCR yielded no text.")
                        else:
                            error_msg_ocr = ocr_result.get('error', 'Unknown OCR error') if ocr_result else 'OCR result was None'
                            print(f"  Page {page_num_ocr + 1} OCR failed: {error_msg_ocr}")
                    except Exception as e_ocr_full_page:
                        print(f"  Error during OCR for page {page_num_ocr + 1}: {str(e_ocr_full_page)}")
            print("--- Finished Full Document OCR ---")

            max_retries = 2
            candidate_number = Path(pdf_path).stem
            feedback_sections.extend([
                f"Candidate Number: {candidate_number}\n"
            ])
            try:
                # ステップ1: グラフ分析（必ず最初に実行し、完了を待つ）
                await self._analyze_and_cache_graphs(doc, pdf_path)

                # 分析結果の確認とログ出力（より詳細に）
                cache_status = {k: len(v) for k, v in self.graph_cache.items() if isinstance(k, int)}
                print("\n=== Detailed Graph Analysis Results ===")
                print(f"- Pages with graphs: {len(cache_status)}")
                print(f"- Graphs per page: {cache_status}")
                print("\nAnalyzing graph-question relationships:")

                # グラフを必要とする設問のリストを作成
                questions_requiring_graphs = []
                for q_id in self.questions.keys():
                    if any(keyword in criterion['description'].lower()
                        for criterion in self.criteria[q_id]
                        for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve']):
                        questions_requiring_graphs.append(q_id)

                print(f"Questions requiring graphs: {questions_requiring_graphs}")

                # 各設問のページ位置を確認
                for q_id in sorted(self.questions.keys()):
                    q_page = self._find_question_page(doc, q_id)
                    print(f"\nQuestion {q_id}:")
                    print(f"- Found on page: {q_page + 1 if q_page is not None else 'Not found'}")
                    if q_page is not None and q_page in self.graph_cache:
                        print(f"- Graph(s) found on same page: {len(self.graph_cache[q_page])}")

                if not self.graph_cache:
                    print("No graphs were found in this PDF")
                else:
                    print("Graph analysis completed successfully")

                # 設問とグラフのマッチングを行う準備
                # 各グラフのスコアを計算して保存する辞書を初期化
                graph_scores = {}

                # すべての設問と利用可能なグラフの組み合わせについてスコアを計算
                for q_id in questions_requiring_graphs:
                    graph_scores[q_id] = []

                    # 検索範囲を広げる（全ページを対象に）
                    for page_num in range(len(doc)):
                        graphs = self._get_cached_graph_analysis(page_num)
                        if not graphs:
                            continue

                        page_text = doc[page_num].get_text("text")

                        for graph_idx, graph in enumerate(graphs):
                            if not isinstance(graph, dict) or not graph.get('success', False):
                                continue

                            # グラフと設問の関連性スコアを計算
                            score = self._calculate_graph_relevance_score(
                                graph, q_id, page_text, page_num, doc)

                            # スコアを保存
                            graph_scores[q_id].append({
                                'page': page_num,
                                'index': graph_idx,
                                'score': score,
                                'graph': graph
                            })

                # 各設問について、関連性スコアの高い順にグラフを並べ替え
                for q_id in questions_requiring_graphs:
                    graph_scores[q_id].sort(key=lambda x: x['score'], reverse=True)
                    print(f"\nSorted graph scores for Question {q_id}:")
                    for idx, item in enumerate(graph_scores[q_id]):
                        print(f"  {idx+1}. Page {item['page']+1}, Graph {item['index']+1}: Score {item['score']:.2f}")

                # 設問-グラフ割り当て用の辞書を初期化
                self.graph_assignments = {}

                # ステップ1: まず、各設問に最も関連性の高いグラフを1つずつ割り当て
                used_graphs = set()  # (page, index) のタプルで使用済みグラフを記録

                for q_id in questions_requiring_graphs:
                    self.graph_assignments[q_id] = []

                    # 関連性の高い順に調べる
                    for item in graph_scores[q_id]:
                        graph_key = (item['page'], item['index'])
                        # まだ他の設問に割り当てられていないグラフで、十分なスコアを持つもの
                        if graph_key not in used_graphs and item['score'] >= 0.2:  # スコアの閾値を0.2に設定
                            # 最初のグラフを割り当て
                            self.graph_assignments[q_id].append(item['graph'])
                            used_graphs.add(graph_key)
                            print(f"Assigned primary graph from Page {item['page']+1}, Graph {item['index']+1} to Question {q_id}")
                            break

                # ステップ2: 使われていないグラフがまだある場合、関連性に基づいて追加のグラフを割り当て
                # 各設問の二つ目のグラフ候補を保存
                second_graph_candidates = {}

                for q_id in questions_requiring_graphs:
                    second_graph_candidates[q_id] = []

                    # 最初のグラフ以降で、まだ使われていないものを探す
                    assigned_count = len(self.graph_assignments[q_id])
                    if assigned_count > 0:  # 既にグラフが割り当てられている場合のみ
                        for item in graph_scores[q_id]:
                            graph_key = (item['page'], item['index'])
                            if graph_key not in used_graphs and item['score'] >= 0.2:  # スコアの閾値を0.2に設定
                                second_graph_candidates[q_id].append({
                                    'page': item['page'],
                                    'index': item['index'],
                                    'score': item['score'],
                                    'graph': item['graph'],
                                    'question': q_id
                                })

                # 全ての候補を一つのリストにまとめる
                all_candidates = []
                for q_id, candidates in second_graph_candidates.items():
                    all_candidates.extend(candidates)

                # スコアの高い順に並び替え
                all_candidates.sort(key=lambda x: x['score'], reverse=True)

                # スコアの高い順に割り当てていく
                for candidate in all_candidates:
                    graph_key = (candidate['page'], candidate['index'])
                    if graph_key not in used_graphs:
                        q_id = candidate['question']
                        self.graph_assignments[q_id].append(candidate['graph'])
                        used_graphs.add(graph_key)
                        print(f"Assigned additional graph from Page {candidate['page']+1}, Graph {candidate['index']+1} to Question {q_id} (score: {candidate['score']:.2f})")

                # ステップ3: まだ割り当てられていないグラフを、グラフが割り当てられていない設問に割り当てる
                unassigned_questions = [q_id for q_id in questions_requiring_graphs if not self.graph_assignments.get(q_id, [])]
                if unassigned_questions:
                    print("\nFinding graphs for unassigned questions...")
                    # グラフを含むページのうち、まだ使われていないグラフを探す
                    unassigned_graphs = []
                    for page_num in self.graph_cache:
                        for graph_idx, graph in enumerate(self.graph_cache[page_num]):
                            graph_key = (page_num, graph_idx)
                            if graph_key not in used_graphs and isinstance(graph, dict) and graph.get('success', False):
                                unassigned_graphs.append({
                                    'page': page_num,
                                    'index': graph_idx,
                                    'graph': graph
                                })

                    # 未割り当てのグラフを未割り当ての設問に割り当てる
                    for q_id in unassigned_questions:
                        if unassigned_graphs:
                            graph_info = unassigned_graphs.pop(0)  # 最初の未割り当てグラフを取得
                            self.graph_assignments[q_id] = [graph_info['graph']]
                            used_graphs.add((graph_info['page'], graph_info['index']))
                            print(f"Assigned unassigned graph from Page {graph_info['page']+1}, Graph {graph_info['index']+1} to Question {q_id}")

                # 割り当て結果を表示
                print("\nFinal graph assignments:")
                for q_id in questions_requiring_graphs:
                    graphs = self.graph_assignments.get(q_id, [])
                    print(f"Question {q_id}: {len(graphs)} graphs assigned")

                # ステップ3: 採点処理（グラフ分析の完了後に開始）
                print("\n=== Step 3: Starting Grading Process ===")

                # Extract text using simplified and reliable method
                full_text = ""
                for page in doc:
                    try:
                        text = page.get_text("text", flags=fitz.TEXT_PRESERVE_LIGATURES | fitz.TEXT_PRESERVE_WHITESPACE)
                        full_text += text + "\n"
                    except Exception as e:
                        print(f"Warning: Text extraction failed for page: {str(e)}")
                        continue

                # 各設問の採点
                for q_id in sorted(self.questions.keys()):
                    print(f"\nProcessing question {q_id} for candidate {candidate_number}")
                    retries = 0
                    success = False

                    while retries < max_retries and not success:
                        try:
                            # Initial answer text extraction (typed text)
                            answer_text_typed = await self._find_answer_section(doc, q_id)
                            if (answer_text_typed is None or not answer_text_typed.strip()) and full_text:
                                print(f"Q {q_id}: _find_answer_section found no/empty answer. Trying _extract_answer_via_gemini for typed text.")
                                answer_text_typed = await self._extract_answer_via_gemini(full_text, q_id)

                            if answer_text_typed:
                                print(f"Q {q_id}: Found typed answer text (first 100 chars): {answer_text_typed[:100]}...")
                            else:
                                print(f"Q {q_id}: No typed answer text found by initial methods.")

                            # --- OCRテキストの準備 (全ページOCRの結果を利用) ---
                            print(f"Q {q_id}: Preparing OCR text from full document scan.")
                            combined_ocr_text_for_q_parts = []
                            if all_pages_ocr_texts: # Check if the dictionary is not empty
                                for p_num, text_content in sorted(all_pages_ocr_texts.items()):
                                    # Ensure text_content is a string before stripping
                                    if isinstance(text_content, str) and text_content.strip():
                                        combined_ocr_text_for_q_parts.append(f"[OCR_TEXT_FROM_PAGE_{p_num + 1}_START]\n{text_content.strip()}\n[OCR_TEXT_FROM_PAGE_{p_num + 1}_END]")
                                    elif isinstance(text_content, str): # It's a string but only whitespace
                                        print(f"  Q {q_id}: OCR text from page {p_num + 1} is whitespace only, skipping.")
                                    else: # Not a string (should not happen if populated correctly)
                                         print(f"  Q {q_id}: OCR text from page {p_num + 1} is not a string (type: {type(text_content)}), skipping.")


                            answer_text_ocr_content = "\n\n".join(combined_ocr_text_for_q_parts).strip()

                            if answer_text_ocr_content:
                                print(f"Q {q_id}: Combined OCR text prepared (first 100 chars): {answer_text_ocr_content[:100]}...")
                            else:
                                print(f"Q {q_id}: No OCR text available from full document scan to include for this question.")

                            # 最終的なanswer_textの組み立て
                            final_answer_text_parts = []
                            # Ensure answer_text_typed is a string, even if empty
                            current_typed_answer = answer_text_typed.strip() if isinstance(answer_text_typed, str) and answer_text_typed.strip() else ""

                            if current_typed_answer:
                                final_answer_text_parts.append(f"[TYPED_TEXT_START]\n{current_typed_answer}\n[TYPED_TEXT_END]")

                            if answer_text_ocr_content:
                                final_answer_text_parts.append(f"[OCR_FULL_DOCUMENT_SCAN_START]\n{answer_text_ocr_content}\n[OCR_FULL_DOCUMENT_SCAN_END]")

                            answer_text = "\n\n".join(final_answer_text_parts).strip()

                            if not answer_text:
                                print(f"Q {q_id}: No answer text (typed or OCR) found after all processing.")
                                answer_text = "No answer provided by student."
                            else:
                                print(f"Q {q_id}: Final combined answer_text for LLM (first 200 chars): {answer_text[:200]}...")
                            # --- OCRテキスト準備ここまで ---

                            # 設問に割り当てられたグラフ分析結果の取得
                            graph_analysis = None
                            if any(keyword in self.questions[q_id].lower() for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve']):
                                assigned_graphs = self.graph_assignments.get(q_id, [])

                                if assigned_graphs:
                                    # 最初のグラフを主要グラフとして使用
                                    graph_analysis = assigned_graphs[0]

                                    # 追加のグラフがある場合は付加情報として使用
                                    if len(assigned_graphs) > 1:
                                        graph_analysis['additional_graphs'] = assigned_graphs[1:]
                                        print(f"Including {len(assigned_graphs) - 1} additional graphs for Question {q_id}")

                            marks, feedback = await self.evaluate_answer(q_id, answer_text, graph_analysis)
                            total_marks += marks  # 合計点数を更新

                            feedback_text = self._format_feedback(q_id, marks, feedback, graph_analysis)
                            feedback_sections.append(feedback_text)
                            success = True

                        except Exception as e:
                            retries += 1
                            print(f"Error on attempt {retries} for {q_id}: {str(e)}")
                            if retries == max_retries:
                                feedback_sections.append(f"Question {q_id}: Error in grading after {max_retries} attempts: {str(e)}")
                            await asyncio.sleep(2 * retries)

                # 個別フィードバックファイルの保存
                output_dir = Path('outputs/exams')
                output_dir.mkdir(parents=True, exist_ok=True)
                output_file = output_dir / f"{candidate_number}.md"

                # 個別フィードバックにはグラフ分析ステータスを含める
                individual_feedback = "\n\n".join(feedback_sections)

                # 結合用フィードバックを作成（グラフ分析ステータスを除外）
                combined_feedback_sections = []
                for section in feedback_sections:
                    if section.startswith("Question"):
                        # 各設問のフィードバックをフォーマット
                        q_id = re.search(r'Question (\w+):', section).group(1)
                        marks = int(re.search(r': (\d+) marks', section).group(1))
                        feedback_text = re.sub(r'Question \w+: \d+ marks\n\n', '', section)
                        formatted_feedback = self._format_combined_feedback(q_id, marks, feedback_text)
                        combined_feedback_sections.append(formatted_feedback)
                    else:
                        # その他のセクション（Candidate Numberなど）はそのまま追加
                        combined_feedback_sections.append(section)

                # 合計点と採点者情報を追加（日付を除外）
                combined_feedback_sections.extend([
                    f"\nTotal marks: {total_marks}",
                    f"\nMarker: {self.marker_name}"
                ])

                # 個別フィードバックを保存
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(individual_feedback)

                # 結合用フィードバックを返す
                return "\n\n".join(combined_feedback_sections)

                print(f"\nGrading completed for {candidate_number}")
                print(f"Total marks: {total_marks}")

            except Exception as e:
                print(f"Warning: Processing error: {str(e)}")
                print("Attempting to continue with available data")

            # 統計情報の更新
            self.statistics['total_exams'] += 1
            self.statistics['processing_time'] += time.time() - start_time

            return "\n\n".join(feedback_sections)

        except Exception as e:
            error_msg = f"Error processing PDF {pdf_path}: {str(e)}"
            print(error_msg)
            return error_msg

        finally:
            if doc:
                try:
                    doc.close()
                except Exception as e:
                    print(f"Warning: Error closing PDF document: {str(e)}")

    def show_statistics(self):
        """Display processing statistics"""
        print("\nProcessing Statistics:")
        print(f"- Total exams processed: {self.statistics['total_exams']}")
        print(f"- Total pages processed: {self.statistics['total_pages']}")
        print(f"- Total API calls: {self.statistics['api_calls']}")
        print(f"- Processing time: {self.statistics['processing_time']:.2f} seconds")
        if self.statistics['total_exams'] > 0:
            print(f"- Average time per exam: {self.statistics['processing_time']/self.statistics['total_exams']:.2f} seconds")
            print(f"- Average API calls per exam: {self.statistics['api_calls']/self.statistics['total_exams']:.1f}")

    def _cleanup_temp_files(self):
        """Clean up temporary files when the program exits"""
        print("\nCleaning: Starting temporary file cleanup...")

        # Remove tracked temporary files
        removed = 0
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    removed += 1
            except Exception as e:
                print(f"Cleanup error: {file_path} - {str(e)}")

        print(f"Cleanup completed: Removed {removed}/{len(self.temp_files)} files")

        # Remove all remaining files in the temp_graphs directory
        import glob
        graph_files = glob.glob(str(self.temp_dir / "graph_*.jpg"))
        removed = 0

        for file_path in graph_files:
            try:
                os.unlink(file_path)
                removed += 1
            except Exception as e:
                print(f"Additional cleanup error: {file_path} - {str(e)}")

        if graph_files:
            print(f"Additional cleanup: Removed {removed}/{len(graph_files)} files")

    def _get_cached_graph_analysis(self, page_num: int) -> Optional[List[Dict]]:
        """Get cached graph analysis for a specific page"""
        if page_num in self.graph_cache:
            result = self.graph_cache[page_num]
            # 返すべき結果がリスト型であることを確認
            if not isinstance(result, list):
                print(f"Warning: Graph cache for page {page_num} is not a list (type: {type(result)})")
                return []
            return result
        return None

    def _find_question_page(self, doc, q_id: str) -> Optional[int]:
        """Find the page number where a specific question appears"""
        question_text = self.questions.get(q_id, "")
        base_q_num = q_id[:-1]  # e.g. "1" from "1a"
        sub_q_char = q_id[-1]  # e.g. "a" from "1a"

        # より柔軟な拡張パターンリスト
        patterns = [
            # 大文字での表記 (E)など)
            rf'{sub_q_char.upper()}\s*\)',
            # 小文字での表記 (e)など)
            rf'{sub_q_char.lower()}\s*\)',
            # 標準フォーマット (1a, 1A)
            rf'{re.escape(q_id)}\s*[:：]',
            # スペース付きフォーマット (1 a), 1 A))
            rf'{base_q_num}\s+{sub_q_char}\)',
            # サブ設問のみ コロン付き (a:, A:, e:, E:)
            rf'{sub_q_char}\s*[:：]',
            # 括弧付きサブ設問 (a), A))
            rf'{sub_q_char}\)',
            # 設問番号と括弧の組み合わせ ([1a], (1e)など)
            rf'[\[\(]{re.escape(q_id)}[\]\)]',
            # ドット付き (1.a, 1.e)
            rf'{base_q_num}\.{sub_q_char}',
            # フレキシブルなマッチング (1a, 1 a, 1.a, 1-a)
            rf'{base_q_num}[\s\.\-]?{sub_q_char}',
            # 設問テキストとの組み合わせ
            rf'{re.escape(question_text[:30])}',
            # 追加パターン1: 数字+スペース+大文字+閉じカッコ (1 A))
            rf'{base_q_num}\s+{sub_q_char.upper()}\)',
            # 追加パターン2: 独立した大文字+閉じカッコ (A))
            rf'{sub_q_char.upper()}\)',
            # 追加パターン3: 数字と大文字の連結形式 (1A))
            rf'{base_q_num}{sub_q_char.upper()}\)',
            # 追加パターン4: 数字+スペース+大文字（閉じカッコなし） (1 A)
            rf'{base_q_num}\s+{sub_q_char.upper()}[\s\.:]',
            # 追加パターン5: 独立した大文字のみ (A. など)
            rf'{sub_q_char.upper()}\.'
        ]

        # 各ページを検索
        for page_num, page in enumerate(doc):
            try:
                text = page.get_text("text", flags=fitz.TEXT_PRESERVE_LIGATURES | fitz.TEXT_PRESERVE_WHITESPACE)

                # スコアベースの検出システムを実装
                max_score = 0
                best_match = None

                print(f"\nSearching for question {q_id} on page {page_num + 1}:")
                print(f"Page text preview: {text[:200]}...")

                # 各パターンでの検索とスコアリング
                for pattern in patterns:
                    print(f"- Trying pattern: {pattern}")
                    match = re.search(pattern, text, flags=re.IGNORECASE)
                    if match:
                        score = 0.0
                        # 特別な優先パターンのスコアリング
                        if pattern == rf'{sub_q_char.upper()}\s*\)':  # E) パターン
                            score = 2.0  # 最高スコア
                        elif pattern == rf'{sub_q_char.lower()}\s*\)':  # e) パターン
                            score = 1.8
                        # 新しい優先パターンのスコアリング - 問題番号+大文字の組み合わせ (1 A) パターン)
                        elif pattern == rf'{base_q_num}\s+{sub_q_char.upper()}\)':
                            score = 2.2  # 最優先
                        # 独立した大文字+閉じカッコ (A) パターン)
                        elif pattern == rf'{sub_q_char.upper()}\)':
                            score = 1.9
                        else:
                            score = 1.0  # 基本スコア

                        # 設問テキストが近くにある場合、スコアを上げる
                        context_range = 200  # 前後200文字を確認
                        match_pos = match.start()
                        start = max(0, match_pos - context_range)
                        end = min(len(text), match_pos + context_range)
                        context = text[start:end]

                        if question_text and question_text[:30] in context:
                            score += 0.5

                        print(f"✓ Pattern match found, score: {score}")
                        if score > max_score:
                            max_score = score
                            best_match = page_num
                    else:
                        print(f"✗ Pattern not found: {pattern}")

                if best_match is not None:
                    print(f"✓ Found question {q_id} on page {best_match + 1} with confidence score: {max_score}")
                    return best_match

            except Exception as e:
                print(f"Warning: Text extraction failed for page {page_num}: {str(e)}")
                continue

        print(f"Warning: Could not locate question {q_id} in PDF using any patterns")
        return None

    def _calculate_graph_relevance_score(self, graph: Union[Dict, bytes], q_id: str, page_text: str, page_num: int, doc) -> float:
        """グラフと設問の関連性スコアを計算する（改良版）"""
        # 必須フィールドのチェック
        required_fields = ['graph_type', 'axes', 'success']

        # 辞書型以外のデータの場合は早期リターン
        if not isinstance(graph, dict):
            print(f"Warning: Graph data is not a dictionary")
            return 0.1

        if not all(field in graph for field in required_fields):
            print(f"Warning: Missing required fields in graph data")
            return 0.3  # 部分的なスコアを返す（一部フィールドが欠けていても関連性の可能性はある)

        # 0. 設問の位置に基づく位置スコアの計算（改善版）
        position_score = 0.0
        question_page = self._find_question_page(doc, q_id)

        if question_page is None:
            # 設問が見つからない場合のデフォルトスコア
            position_score = 0.05
        else:
            page_distance = abs(question_page - page_num)

            # より詳細な位置スコアリング
            if page_distance == 0:  # 同じページ
                position_score = 1.5
                # 設問とグラフの順序も考慮
                try:
                    question_text = doc[question_page].get_text("text")
                    graph_pos = page_text.find(graph.get('raw_response', ''))
                    question_pos = question_text.find(self.questions[q_id])
                    if question_pos >= 0 and graph_pos >= 0:
                        # グラフが設問の直後にある場合、さらにスコアを上げる
                        if graph_pos > question_pos:
                            position_score += 0.3
                except Exception as e:
                    print(f"Warning: Error analyzing text positions: {str(e)}")

            elif page_distance == 1:  # 隣接ページ
                position_score = 1.2
                # グラフが設問の次のページにある場合、スコアを上げる
                if page_num > question_page:
                    position_score += 0.2

            elif page_distance == 2:  # 2ページ以内
                position_score = 0.7
            elif page_distance == 3:  # 3ページ以内
                position_score = 0.3
            else:
                position_score = 0.05  # 遠いページのグラフ

        print(f"\nPosition score calculation for Q{q_id} (Page {page_num + 1}):")
        if question_page is not None:
            page_distance = abs(question_page - page_num)
            print(f"- Question page: {question_page + 1}")
            print(f"- Distance: {page_distance}")
        else:
            print(f"- Question page: Not found")
        print(f"- Base position score: {position_score:.2f}")

        import re  # モジュールを明示的にインポート

        # 1. 設問番号の柔軟なマッチング
        base_q_num = q_id[:-1]  # e.g. "1" from "1a"
        sub_q_char = q_id[-1]  # e.g. "a" from "1a"

        question_patterns = [
            rf'{re.escape(q_id)}\s*[:：]',  # 1a:
            rf'{base_q_num}\s+{sub_q_char}\)',  # 1 a)
            rf'{sub_q_char}\)',  # a)
            rf'{sub_q_char}\s*[:：]'  # a:
        ]

        # 設問番号が同じページにあるかチェック
        if any(re.search(pattern, page_text) for pattern in question_patterns):
            return position_score + 0.5  # 位置スコアに加算

        # 2. グラフタイプと問題文・解答のキーワードマッチング
        question_text = self.questions.get(q_id, "").lower()
        answer_content = page_text.lower()  # ページ内の解答テキスト
        graph_type = graph.get('graph_type', '').lower()
        graph_description = graph.get('economic_interpretation', '').lower()

        # 経済学の概念に関するキーワードリスト
        concept_keywords = [
            'supply', 'demand', 'equilibrium', 'price', 'quantity',
            'market', 'curve', 'elasticity', 'shift', 'movement',
            'tax', 'subsidy', 'surplus', 'shortage', 'deadweight'
        ]

        # グラフの特徴に関するキーワードリスト
        graph_keywords = [
            'increase', 'decrease', 'rise', 'fall', 'shift',
            'intersection', 'slope', 'curve', 'point', 'line',
            'axis', 'maximum', 'minimum', 'optimal'
        ]

        # 3. 数値の一致検証（新機能）
        numeric_match_score = 0.0

        # グラフ内の数値を抽出
        graph_numbers = set()
        for text_field in [graph_type, graph_description, graph.get('raw_response', '')]:
            if not text_field:
                continue
            # 数値パターンの抽出 (小数点を含む)
            numbers = re.findall(r'\b\d+(?:\.\d+)?\b', text_field)
            graph_numbers.update(numbers)

        # 設問内の数値を抽出
        question_numbers = set(re.findall(r'\b\d+(?:\.\d+)?\b', question_text))

        # 一致する数値があれば高いスコアを付与
        matching_numbers = graph_numbers.intersection(question_numbers)
        if matching_numbers:
            numeric_match_score = min(0.5, len(matching_numbers) * 0.2)

        # スコアの計算（問題文とグラフの説明の一致度）
        concept_score = sum(
            0.4 for kw in concept_keywords
            if (kw in question_text and kw in graph_type) or
               (kw in question_text and kw in graph_description)
        )

        # グラフの特徴との一致度
        feature_score = sum(
            0.3 for kw in graph_keywords
            if (kw in answer_content and kw in graph_description) or
               (kw in question_text and kw in graph_description)
        )

        # 4. 軸ラベルのマッチング
        axes = graph.get('axes', {})
        x_axis = axes.get('x', '').lower()
        y_axis = axes.get('y', '').lower()

        axis_keywords = [
            'price', 'quantity', 'cost', 'revenue', 'interest rate', 'wage', 'utility'
        ]

        axis_score = sum(
            0.3 for kw in axis_keywords
            if kw in question_text and kw in (x_axis + y_axis)
        )

        # 5. 生のAPIレスポンスからの経済概念マッチング
        raw_response = graph.get('raw_response', '').lower()
        response_score = sum(
            0.2 for kw in concept_keywords
            if kw in question_text and kw in raw_response
        )

        # 6. 総合スコアの計算（位置スコアを重視）
        total_score = (
            position_score * 0.5 +  # 位置に基づくスコア（最重要）
            numeric_match_score * 0.3 +  # 数値の一致（重要）
            concept_score * 0.2 +  # 概念の一致
            feature_score * 0.2 +  # 特徴の一致
            axis_score * 0.2 +  # 軸の一致
            response_score * 0.1   # APIレスポンスの一致
        )

        print(f"\n=== Detailed Graph Relevance Score for Q{q_id} ===")
        print(f"1. Position Score: {position_score:.2f} * 0.5 = {position_score * 0.5:.2f}")
        print(f"2. Numeric Match: {numeric_match_score:.2f} * 0.3 = {numeric_match_score * 0.3:.2f}")
        print(f"3. Concept Match: {concept_score:.2f} * 0.2 = {concept_score * 0.2:.2f}")
        print(f"4. Feature Match: {feature_score:.2f} * 0.2 = {feature_score * 0.2:.2f}")
        print(f"5. Axis Match: {axis_score:.2f} * 0.2 = {axis_score * 0.2:.2f}")
        print(f"6. Response Match: {response_score:.2f} * 0.1 = {response_score * 0.1:.2f}")
        print(f"Total Score: {total_score:.2f}")

        return min(1.0, total_score)  # 最大スコアは1.0に制限

    def _format_feedback(self, q_id: str, marks: int, feedback: str, graph_analysis: Dict = None) -> str:
        """採点フィードバックのフォーマット"""
        requires_graph = any(
            keyword in criterion['description'].lower()
            for criterion in self.criteria[q_id]
            for keyword in ['graph', 'plot', 'diagram', 'figure', 'curve']
        )

        analysis_text = ""
        if requires_graph:
            if graph_analysis and graph_analysis.get("success"):
                analysis = graph_analysis

                # 主要グラフの分析テキスト
                analysis_text = (
                    f"\n=== Graph Overview ===\n"
                    f"1. Graph Type:\n"
                    f"   - {self._determine_graph_type(analysis)}\n\n"
                    f"2. Axes:\n"
                    f"   - X-axis: {self._interpret_axis_label(analysis, 'x')}\n"
                    f"   - Y-axis: {self._interpret_axis_label(analysis, 'y')}\n\n"
                    f"3. Key Features:\n"
                    f"   - Trends: {', '.join(analysis['trends'])}\n"
                    f"   - Critical Points: {', '.join(analysis['points_of_interest'])}\n\n"
                    f"4. Economic Interpretation:\n"
                    f"   - {analysis['economic_interpretation']}\n\n"
                    f"5. Quality Assessment:\n"
                    f"   - {analysis['quality_assessment']}\n\n"
                    f"6. Detailed Analysis:\n"
                    f"{analysis.get('raw_response', 'No detailed analysis available.')}\n"
                )

                # 追加のグラフがある場合、それらの情報も含める
                if 'additional_graphs' in analysis and isinstance(analysis['additional_graphs'], list):
                    additional_graphs = [g for g in analysis['additional_graphs'] if isinstance(g, dict) and g.get('success', False)]
                    if additional_graphs:
                        for idx, add_graph in enumerate(additional_graphs):
                            analysis_text += f"\n=== Additional Graph {idx+1} ===\n"
                            analysis_text += (
                                f"1. Graph Type:\n"
                                f"   - {self._determine_graph_type(add_graph)}\n\n"
                                f"2. Axes:\n"
                                f"   - X-axis: {self._interpret_axis_label(add_graph, 'x')}\n"
                                f"   - Y-axis: {self._interpret_axis_label(add_graph, 'y')}\n\n"
                                f"3. Key Features:\n"
                                f"   - Trends: {', '.join(add_graph.get('trends', ['Not specified']))}\n"
                                f"   - Critical Points: {', '.join(add_graph.get('points_of_interest', ['Not specified']))}\n\n"
                                f"4. Economic Interpretation:\n"
                                f"   - {add_graph.get('economic_interpretation', 'Shows economic relationships.')}\n\n"
                                f"5. Quality Assessment:\n"
                                f"   - {add_graph.get('quality_assessment', 'Basic economic graph presentation.')}\n\n"
                                f"6. Detailed Analysis:\n"
                                f"{add_graph.get('raw_response', 'No detailed analysis available for this additional graph.')}\n"
                            )

                # グラフ分析結果をフィードバックの先頭に追加
                feedback = f"{analysis_text}\n\n{feedback}"

            else:
                # グラフが見つからない場合の詳細な情報
                analysis_text = (
                    f"\n=== Graph Analysis Status ===\n"
                    f"1. Graph Search Details:\n"
                    f"   - Pages searched: {len(self.graph_cache)}\n"
                    f"   - Graphs found: {sum(len(v) for v in self.graph_cache.values() if isinstance(v, list))}\n"
                    f"   - Graph locations: {[k+1 for k in self.graph_cache.keys() if isinstance(k, int)]}\n\n"
                    f"2. Possible Reasons:\n"
                    f"   - Graph may be on a different page\n"
                    f"   - Graph may not meet analysis criteria\n"
                    f"   - Graph may be poorly drawn or unclear\n"
                )
                # グラフ分析ステータスをフィードバックの先頭に追加
                feedback = f"{analysis_text}\n\n{feedback}"
        elif requires_graph:
            # グラフが見つからない場合の詳細な説明
            analysis_text = (
                f"\n=== Graph Analysis Status ===\n"
                f"1. Graph Search Details:\n"
                f"   - Pages searched: {len(self.graph_cache)}\n"
                f"   - Graphs found: {sum(len(v) for v in self.graph_cache.values() if isinstance(v, list))}\n"
                f"   - Graph locations: {[k+1 for k in self.graph_cache.keys() if isinstance(k, int)]}\n\n"
                f"2. Possible Reasons:\n"
                f"   - Graph may be on a different page\n"
                f"   - Graph may not meet analysis criteria\n"
                f"   - Graph may be poorly drawn or unclear\n"
            )

            # グラフがない場合のフィードバックを修正
            feedback = feedback.replace(
                "loses significant marks due to the missing graph",
                f"loses significant marks because:\n{analysis_text}"
            )

        # 個別フィードバック用の完全なフィードバックを返す
        return (
            f"Question {q_id}: {marks} marks\n\n"
            f"{feedback}"
        )

    def _format_combined_feedback(self, q_id: str, marks: int, feedback: str) -> str:
        """結合用フィードバックのフォーマット（グラフ分析ステータスを完全に除外）"""
        # グラフ分析ステータスセクションとその後の理由部分を完全に削除
        feedback = re.sub(
            r'=== Graph Analysis Status ===.*?Possible Reasons:.*?(?=\n\n|\Z)',
            '',
            feedback,
            flags=re.DOTALL
        )

        return (
            f"Question {q_id}: {marks} marks\n\n"
            f"{feedback.strip()}"
        )

    def _interpret_axis_label(self, graph_analysis: Dict, axis: str) -> str:
        """
        軸ラベル（X軸またはY軸）の解釈を行い、単一文字表記（'P'や'Q'など）も含めて
        適切な経済変数に変換する

        Args:
            graph_analysis: グラフ分析結果の辞書
            axis: 'x'または'y'を指定

        Returns:
            解釈された軸ラベル
        """
        # グラフ分析結果から軸データを取得
        if not isinstance(graph_analysis, dict):
            return "Unknown economic variable"

        axes = graph_analysis.get('axes', {})
        if not isinstance(axes, dict):
            return "Unknown economic variable"

        label = axes.get(axis, "").strip()
        raw_response = graph_analysis.get('raw_response', '').lower()

        # "No labelling"の場合は特別処理
        if label.lower() == "no labelling" or not label:
            # raw_responseからの抽出を試みる
            return self._extract_axis_from_raw_response(raw_response, axis)

        # 明示的な軸ラベルがある場合の処理
        # 単一文字の場合、拡張して返す
        single_char_mappings = {
            'p': "Price (P)",
            'q': "Quantity (Q)",
            'c': "Cost (C)",
            'r': "Revenue (R)",
            'y': "Income/Output (Y)",
            'i': "Interest Rate (i)",
            'l': "Labor (L)",
            'k': "Capital (K)",
            'w': "Wage (W)",
            's': "Supply (S)",
            'd': "Demand (D)"
        }

        # 単一文字かどうかチェック
        if len(label.strip()) == 1 and label.lower() in single_char_mappings:
            return single_char_mappings[label.lower()]
        else:
            # ラベルが既に意味のある言葉である場合はそのまま返す（最初の文字を大文字に）
            return label[0].upper() + label[1:] if len(label) > 1 else label.upper()

    def _extract_axis_from_raw_response(self, raw_response: str, axis: str) -> str:
        """
        APIレスポンステキストから軸ラベル情報を抽出する

        Args:
            raw_response: APIからの生のレスポンステキスト
            axis: 'x'または'y'を指定

        Returns:
            抽出された軸ラベル
        """
        # 軸固有のキーワードとパターンを定義
        axis_specific_info = {
            'x': {
                'patterns': [
                    r'x[\s-]?axis[\s:]*(is|shows|represents|displays|indicates|depicts)[\s:]*([\w\s]+)',
                    r'horizontal axis[\s:]*(is|shows|represents|displays|indicates|depicts)[\s:]*([\w\s]+)',
                    r'([\w\s]+)[\s-]on the x[\s-]?axis',
                    r'([\w\s]+)[\s-]on the horizontal axis',
                    r'x-axis:[\s]*([\w\s,]+)',
                    r'horizontal axis:[\s]*([\w\s,]+)'
                ],
                'keywords': ['quantity', 'output', 'income', 'production', 'consumption', 'time'],
                'single_chars': {'q': "Quantity", 'y': "Output/Income", 'x': "Quantity/Input"},
                'default': "Quantity (horizontal axis)"
            },
            'y': {
                'patterns': [
                    r'y[\s-]?axis[\s:]*(is|shows|represents|displays|indicates|depicts)[\s:]*([\w\s]+)',
                    r'vertical axis[\s:]*(is|shows|represents|displays|indicates|depicts)[\s:]*([\w\s]+)',
                    r'([\w\s]+)[\s-]on the y[\s-]?axis',
                    r'([\w\s]+)[\s-]on the vertical axis',
                    r'y-axis:[\s]*([\w\s,]+)',
                    r'vertical axis:[\s]*([\w\s,]+)'
                ],
                'keywords': ['price', 'cost', 'revenue', 'interest rate', 'wage', 'utility'],
                'single_chars': {'p': "Price", 'c': "Cost", 'r': "Revenue", 'i': "Interest Rate"},
                'default': "Price (vertical axis)"
            }
        }

        # 該当する軸の情報を取得
        axis_info = axis_specific_info[axis]

        # パターンマッチングを試行
        for pattern in axis_info['patterns']:
            match = re.search(pattern, raw_response, re.IGNORECASE)
            if match:
                # パターンによって抽出グループが異なる
                if 'is|shows|represents|displays|indicates|depicts' in pattern:
                    extracted = match.group(2)
                else:
                    # ':' が含まれるパターン
                    extracted = match.group(1) if match.lastindex == 1 else match.group(2)

                if extracted and len(extracted.strip()) > 1:  # 単一文字以上の場合のみ採用
                    # 余分な記号や空白を除去し、最初の文字を大文字に
                    cleaned = re.sub(r'[,:;]', '', extracted).strip()
                    return cleaned[0].upper() + cleaned[1:] if len(cleaned) > 1 else cleaned.upper()

        # キーワードベースの検索（軸に関連する特徴的な単語を探す）
        for keyword in axis_info['keywords']:
            if keyword in raw_response:
                # キーワードの周辺テキストをチェック（より具体的な表現を探す）
                context_pattern = rf'({keyword}[\s\w]{{0,20}})'
                matches = re.findall(context_pattern, raw_response)
                if matches:
                    best_match = max(matches, key=len).strip()
                    # 余分な記号や空白を除去し、最初の文字を大文字に
                    cleaned = re.sub(r'[,:;]', '', best_match).strip()
                    return cleaned[0].upper() + cleaned[1:] if len(cleaned) > 1 else cleaned.upper()
                return keyword[0].upper() + keyword[1:]  # キーワードをそのまま返す

        # 単一文字の検索
        for char, meaning in axis_info['single_chars'].items():
            if re.search(fr'\b{char}\b', raw_response, re.IGNORECASE):
                return meaning

        # デフォルト値を返す
        return axis_info['default']

    def _determine_graph_type(self, graph_analysis: Dict) -> str:
        """
        グラフ分析結果から、より具体的な経済学的グラフタイプを判定する
        単に「Supply and demand」ではなく、具体的な政策効果や経済概念を特定する

        Args:
            graph_analysis: グラフ分析結果の辞書

        Returns:
            より具体的なグラフタイプの説明
        """
        if not isinstance(graph_analysis, dict):
            return "Economic graph (unspecified type)"

        raw_response = graph_analysis.get('raw_response', '').lower()
        graph_type = graph_analysis.get('graph_type', '').lower()
        economic_interpretation = graph_analysis.get('economic_interpretation', '').lower()

        # 経済政策・効果に関する特定パターン
        policy_effects = {
            'tax': ['tax', 'taxation', 'tax burden', 'tax incidence', 'tax revenue', 'deadweight loss', 'welfare loss'],
            'subsidy': ['subsidy', 'subsidies', 'government support', 'price support', 'producer support'],
            'price ceiling': ['price ceiling', 'price cap', 'maximum price', 'rent control'],
            'price floor': ['price floor', 'minimum price', 'minimum wage'],
            'trade': ['tariff', 'quota', 'import', 'export', 'trade barrier', 'protectionism', 'free trade'],
            'externality': ['externality', 'externalities', 'social cost', 'private cost', 'social benefit', 'private benefit', 'pollution'],
            'monopoly': ['monopoly', 'monopolist', 'price maker', 'profit maximization', 'deadweight loss', 'market power'],
            'perfect competition': ['perfect competition', 'competitive market', 'price taker', 'zero economic profit'],
            'marginal analysis': ['marginal cost', 'marginal benefit', 'marginal revenue', 'marginal utility', 'diminishing returns'],
            'elasticity': ['elasticity', 'elastic', 'inelastic', 'price elasticity', 'income elasticity', 'cross elasticity'],
            'market equilibrium': ['equilibrium', 'market clearing', 'shortage', 'surplus', 'disequilibrium'],
            'macroeconomic': ['aggregate demand', 'aggregate supply', 'ad-as model', 'ad/as', 'fiscal policy', 'monetary policy'],
            'is-lm model': ['is-lm', 'is curve', 'lm curve', 'interest rate', 'income', 'liquidity preference'],
            'phillips curve': ['phillips curve', 'inflation', 'unemployment', 'stagflation', 'short-run', 'long-run'],
            'production function': ['production function', 'total product', 'marginal product', 'average product', 'isoquant'],
            'cost curves': ['cost curve', 'average cost', 'marginal cost', 'total cost', 'fixed cost', 'variable cost'],
            'game theory': ['game theory', 'nash equilibrium', 'prisoner\'s dilemma', 'dominant strategy', 'payoff matrix'],
            'indifference curves': ['indifference curve', 'budget constraint', 'utility maximization', 'consumer choice'],
            'labor market': ['labor market', 'wage determination', 'labor supply', 'labor demand', 'unemployment']
        }

        # 最も具体的なマッチを検索
        best_match = None
        max_matches = 0

        combined_text = f"{raw_response} {graph_type} {economic_interpretation}"

        for policy, keywords in policy_effects.items():
            matches = sum(1 for keyword in keywords if keyword in combined_text)
            if matches > max_matches:
                max_matches = matches
                best_match = policy

        # 基本的な供給・需要グラフの確認
        if not best_match and ('supply' in combined_text and 'demand' in combined_text):
            return "Supply and Demand Diagram"

        # マッチした場合は具体的なグラフタイプを返す
        if best_match and max_matches >= 2:  # 少なくとも2つのキーワードがマッチした場合
            if best_match == 'tax':
                return "Tax Impact Analysis Diagram"
            elif best_match == 'subsidy':
                return "Subsidy Effect Diagram"
            elif best_match == 'price ceiling':
                return "Price Ceiling Analysis Diagram"
            elif best_match == 'price floor':
                return "Price Floor Analysis Diagram"
            elif best_match == 'trade':
                return "International Trade Analysis Diagram"
            elif best_match == 'externality':
                return "Externality Analysis Diagram"
            elif best_match == 'monopoly':
                return "Monopoly Market Structure Diagram"
            elif best_match == 'perfect competition':
                return "Perfect Competition Analysis"
            elif best_match == 'marginal analysis':
                return "Marginal Analysis Diagram"
            elif best_match == 'elasticity':
                return "Elasticity Analysis Diagram"
            elif best_match == 'market equilibrium':
                return "Market Equilibrium Diagram"
            elif best_match == 'macroeconomic':
                return "AD-AS Macroeconomic Model"
            elif best_match == 'is-lm model':
                return "IS-LM Model Diagram"
            elif best_match == 'phillips curve':
                return "Phillips Curve Analysis"
            elif best_match == 'production function':
                return "Production Function Diagram"
            elif best_match == 'cost curves':
                return "Cost Curve Analysis"
            elif best_match == 'game theory':
                return "Game Theory Diagram"
            elif best_match == 'indifference curves':
                return "Consumer Choice Analysis with Indifference Curves"
            elif best_match == 'labor market':
                return "Labor Market Analysis Diagram"

        # 元のグラフタイプがあれば返す
        if graph_analysis.get('graph_type'):
            return graph_analysis['graph_type']

        # APIレスポンスから供給と需要が検出された場合
        if 'supply' in raw_response and 'demand' in raw_response:
            return "Supply and Demand Diagram"

        # デフォルト値
        return "Economic Analysis Diagram"
