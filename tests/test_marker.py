import pytest
from pathlib import Path
import os
from ..exam_marker.marker import ExamMarker
import google.generativeai as genai

@pytest.fixture
def mock_model():
    class MockModel:
        async def generate_content(self, prompt):
            class MockResponse:
                text = '''
                {
                    "marks_awarded": 8,
                    "feedback": "Good understanding of economic concepts shown",
                    "breakdown": [
                        {
                            "criterion": "calculation steps",
                            "marks": 5,
                            "comment": "Clear mathematical working shown"
                        },
                        {
                            "criterion": "economic interpretation",
                            "marks": 3,
                            "comment": "Good economic reasoning"
                        }
                    ]
                }
                '''
            return MockResponse()
    return MockModel()

@pytest.fixture
def sample_env(tmp_path):
    env_vars = {
        'GEMINI_API_KEY': 'test_key',
        'QUESTION_TEXT_1a': 'Calculate equilibrium price',
        'SOLUTION_1a': 'Step 1: Set Qd=Qs...',
        'QUESTION_1a': '5,calculation steps|3,economic interpretation'
    }
    for key, value in env_vars.items():
        os.environ[key] = value
    return env_vars

async def test_exam_marker_initialization(mock_model, sample_env):
    marker = ExamMarker(model=mock_model, marker_name="Test Marker")
    assert marker.questions['1a'] == 'Calculate equilibrium price'
    assert len(marker.criteria['1a']) == 2
    assert marker.criteria['1a'][0]['marks'] == 5

async def test_answer_evaluation(mock_model, sample_env):
    marker = ExamMarker(model=mock_model, marker_name="Test Marker")
    marks, feedback = await marker.evaluate_answer('1a', 'Test answer')
    assert marks == 8
    assert 'Good understanding' in feedback

async def test_pdf_processing(mock_model, sample_env, tmp_path):
    # Create a test PDF file
    pdf_dir = tmp_path / "pdfs"
    pdf_dir.mkdir()
    test_pdf = pdf_dir / "test_exam.pdf"
    test_pdf.write_bytes(b'%PDF-1.4\n%Test PDF')  # Minimal PDF content
    
    output_dir = tmp_path / "output"
    marker = ExamMarker(model=mock_model, marker_name="Test Marker")
    
    await marker.process_pdf_directory(
        pdf_dir=str(pdf_dir),
        output_dir=str(output_dir)
    )
    
    assert (output_dir / "test_exam.md").exists()
    assert (output_dir / "combined_feedback.md").exists()