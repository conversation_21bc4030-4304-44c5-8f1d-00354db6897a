import sys
import os
sys.path.append(os.path.abspath("src"))

# GraphAnalyzerクラスをインポート
from exam_marker.graph_analyzer import GraphAnalyzer

def test_extract_axis_label():
    """_extract_axis_label関数をテストする"""
    
    # GraphAnalyzerのインスタンスを作成
    # APIキーは実際には使用しないのでダミーで十分
    analyzer = GraphAnalyzer("dummy_api_key")
    
    # テスト用のテキスト（Gemini APIから返されるような回答）
    test_texts = [
        # テスト1：単一文字ラベル "P" と "Q"
        """Graph Type: Supply and Demand Curves
X-axis: The horizontal axis (labeled "Q") represents quantity.
Y-axis: The vertical axis (labeled "P") represents price.
Trends: Downward-sloping demand curve, upward-sloping supply curve, intersecting at equilibrium
Key Points: Equilibrium price and quantity at intersection, consumer surplus above equilibrium price and below demand curve, producer surplus below equilibrium price and above supply curve
Economic Interpretation: Basic supply and demand model showing market equilibrium""",
        
        # テスト2：単一文字ラベル (P) と (Q)
        """Graph Type: Supply and Demand with Price Floor
X-axis: Quantity (labeled Q)
Y-axis: Price (labeled P)
Trends: Supply curve slopes upward, demand curve slopes downward, horizontal line representing price floor above equilibrium
Key Points: Market equilibrium, price floor level, surplus quantity
Economic Interpretation: Effect of price floor creating market surplus""",
        
        # テスト3：カッコ内の単一文字
        """Graph Type: Market with Tax
X-axis: The horizontal axis (labeled (Q)) shows quantity
Y-axis: The vertical axis (labeled (P)) shows price
Trends: Upward-sloping supply curve, downward-sloping demand curve, tax wedge between prices
Key Points: Pre-tax equilibrium, post-tax equilibrium, tax incidence
Economic Interpretation: Impact of per-unit tax on market equilibrium and tax burden distribution""",
        
        # テスト4：ラベルなしの軸
        """Graph Type: Economic Diagram
X-axis: No labelling
Y-axis: No labelling
Trends: Two intersecting lines
Key Points: Intersection point
Economic Interpretation: Generic economic relationship""",
        
        # テスト5：引用符なしの単一文字
        """Graph Type: Supply and Demand Model
X-axis: The horizontal axis labeled Q represents quantity.
Y-axis: The vertical axis labeled P represents price.
Trends: Downward-sloping demand, upward-sloping supply
Key Points: Equilibrium at intersection
Economic Interpretation: Basic market equilibrium model"""
    ]
    
    # 各テキストに対して軸ラベル抽出をテスト
    for i, text in enumerate(test_texts):
        print(f"\n=== テスト {i+1} ===")
        print(f"テキスト抜粋: {text[:100]}...")
        
        # X軸とY軸のラベルを抽出
        x_label = analyzer._extract_axis_label(text, "x")
        y_label = analyzer._extract_axis_label(text, "y")
        
        print(f"検出したX軸ラベル: {x_label}")
        print(f"検出したY軸ラベル: {y_label}")

if __name__ == "__main__":
    test_extract_axis_label()