# 試験採点システム

このプロジェクトは、経済学の試験答案を自動採点するシステムです。Google Geminiの最新モデル「gemini-2.5-flash-preview-04-17」を活用して、PDFフォーマットの試験答案を分析し、採点とフィードバックを自動生成します。教育機関で経済学試験の採点業務を効率化し、教員の負担を軽減しながら一貫性のある詳細なフィードバックを提供します。

## 概要

このシステムは、Google Gemini APIを使用してPDF形式の試験答案を分析し、数学的な計算や経済学の概念を処理し、事前に定義された採点基準に基づいて包括的なフィードバックを提供します。一貫性と信頼性の高い採点を実現するため、temperature: 0.1とtop_p: 0.95で設定しています。経済学のグラフや図表の高度な分析機能も実装されています。

## 前提条件

- Python 3.10+
- Google Gemini APIキー（[Google AI Studio](https://aistudio.google.com/)から取得）
- PDFフォーマットの試験答案ファイル

## インストール

```bash
# リポジトリのクローン
git clone <repository-url>
cd exam-marker

# uvを使用して依存関係をインストール
uv pip install -e .

# もしくは

uv sync
pip install -e .
```

## 設定

以下の構造で`.env`ファイルを作成します：

```bash
# API設定
GEMINI_API_KEY=あなたのAPIキーをここに入力

# 問題文（試験内容に応じてカスタマイズ）
QUESTION_TEXT_1a=均衡価格と数量を求めなさい...

# 解答（試験の要件に合わせて調整）
SOLUTION_1a=均衡状態では: 詳細な解答手順...

# 採点基準（形式: 点数,基準|点数,基準）
QUESTION_1a=3,計算過程|2,正しい結果

# グラフ分析設定（経済学の図表分析用にカスタマイズ）
GRAPH_ANALYSIS="あなたは経済学のグラフを分析する専門家です..."

# オプション設定
MARKER_NAME="採点者名"

注意：これらのプロンプトと基準は、特定の試験内容、経済学の概念、
および評価要件に合わせてカスタマイズしてください。システムは
これらの設定に基づいて分析と採点を行います。
```

## 使用方法

### GUIモード

パッケージを実行するだけです：

```bash
python -m exam_marker
```

以下のプロンプトに従って操作します：
1. PDF形式の試験答案が含まれているディレクトリを選択
2. 採点結果の保存先を選択

### CLIモード

```bash
python -m exam_marker /path/to/pdf/directory -o /path/to/output/directory -m "採点者名"
```

### 出力

各試験答案に対して以下が生成されます：
- 個別のフィードバックファイル（`[受験者番号].md`）
- 統合フィードバックファイル（`combined_feedback.md`）

## 出力フォーマット

個別フィードバックファイルには以下が含まれます：
```markdown
# 試験フィードバック
受験者: [ID]
合計点: [総点]

## 問題 1a
得点: [点数]
[詳細なフィードバック]
[基準ごとの内訳]

[グラフ分析結果（該当する場合）]
1. グラフの種類:
   - [識別されたタイプ]
2. 構成要素:
   - X軸: [説明]
   - Y軸: [説明]
3. 主要な特徴:
   - トレンド: [特定された傾向]
   - 重要点: [キーポイント]
4. 経済学的解釈:
   [解釈の詳細]

[...他の問題...]

採点者: [採点者名]
```

## 特徴

- 複数の答案PDFの一括処理
- グラフと文章回答の総合的な評価による採点及びフィードバックの生成
- 事前に定義された基準に基づく構造化された採点
- 採点進捗状況の追跡とエラーハンドリング

## プロジェクト構造

### システムアーキテクチャ

```mermaid
sequenceDiagram
    participant ユーザー
    participant CLI/GUI
    participant 採点システム
    participant PDF処理
    participant グラフ解析
    participant GeminiAPI

    ユーザー->>CLI/GUI: 試験PDFディレクトリを選択
    CLI/GUI->>採点システム: APIキーで初期化

    loop 各PDFファイル
        採点システム->>PDF処理: PDFを開いて検証
        PDF処理-->>採点システム: ドキュメントオブジェクト

        採点システム->>グラフ解析: グラフ分析開始
        グラフ解析->>PDF処理: PDFから画像を抽出
        PDF処理-->>グラフ解析: 画像データ
        グラフ解析->>GeminiAPI: 画像分析リクエスト
        Note over GeminiAPI: gemini-2.5-flash-preview-04-17を使用
        GeminiAPI-->>グラフ解析: 分析結果
        グラフ解析-->>採点システム: グラフデータをキャッシュに保存

        loop 各設問
            採点システム->>PDF処理: 回答部分抽出
            PDF処理-->>採点システム: 回答テキスト

            alt グラフが必要な場合
                採点システム->>採点システム: キャッシュから関連グラフを検索
                採点システム->>GeminiAPI: 回答とグラフの評価リクエスト
                Note over GeminiAPI: gemini-2.5-flash-preview-04-17を使用
                GeminiAPI-->>採点システム: レート制限待機（25秒）
                GeminiAPI-->>採点システム: 評価結果（JSON）
            else グラフが不要な場合
                採点システム->>GeminiAPI: 回答の評価リクエスト
                Note over GeminiAPI: gemini-2.5-flash-preview-04-17を使用
                GeminiAPI-->>採点システム: レート制限待機（25秒）
                GeminiAPI-->>採点システム: 評価結果（JSON）
            end

            採点システム->>採点システム: フィードバックのフォーマット
        end

        採点システム->>CLI/GUI: フィードバックファイルを保存
    end

    CLI/GUI->>ユーザー: 完了状態を表示
```

### コンポーネント構成

- **__main__.py**: アプリケーションのメインエントリーポイント
  - GUIとCLIインターフェースの提供
  - 環境設定の読み込みと初期化
  - 全体的な処理フローの制御

- **marker.py**: 採点システムのコア機能
  - 採点基準の管理と適用
  - Gemini APIとの連携
  - 非同期処理による効率的な採点
  - 採点統計の収集と管理

- **graph_analyzer.py**: グラフ分析システム
  - PDFからの画像抽出
  - グラフ特徴の検出
  - 経済学的解釈
  - 採点プロセスとの統合

- **section.py**: PDF文書の処理と構造化
  - 階層的なセクション管理
  - 回答部分の特定と抽出
  - テキストの前処理

- **lang/**: 多言語サポート機能
  - 各言語固有の処理実装
  - 言語の自動検出と選択
  - メッセージのローカライズ

## 開発

テストの実行：
```bash
pytest tests/
```

## ライセンス

詳細はLICENSEファイルを参照してください。
