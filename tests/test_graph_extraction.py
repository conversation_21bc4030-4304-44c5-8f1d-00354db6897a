import asyncio
import os
from pathlib import Path
import PIL.Image
from src.exam_marker.graph_analyzer import GraphAnalyzer

class GraphExtractionDebugger:
    def __init__(self, pdf_path: str, debug_dir: str = "debug_output"):
        """
        Initialize the debugger with a specific PDF file
        
        Args:
            pdf_path: Path to the PDF file to analyze
            debug_dir: Directory to save debug output
        """
        self.pdf_path = pdf_path
        self.debug_dir = debug_dir
        self.ensure_debug_dir()
        
        # Load environment variables from .env
        from dotenv import load_dotenv
        load_dotenv()
        
        # Initialize GraphAnalyzer
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable not found")
        self.analyzer = GraphAnalyzer(api_key)

    def ensure_debug_dir(self):
        """Create debug output directory if it doesn't exist"""
        Path(self.debug_dir).mkdir(parents=True, exist_ok=True)

    def save_debug_images(self, images: list[PIL.Image.Image], page_num: int):
        """Save extracted images for visual inspection"""
        # デバッグ出力ディレクトリが存在しない場合は作成
        if not os.path.exists(self.debug_dir):
            os.makedirs(self.debug_dir)
            print(f"Created debug output directory: {self.debug_dir}")

        for idx, img in enumerate(images):
            try:
                debug_path = os.path.join(self.debug_dir, f"page_{page_num}_image_{idx}.png")
                img.save(debug_path)
                print(f"Saved debug image: {debug_path}")
                print(f"Image properties - Size: {img.size}, Mode: {img.mode}")
            except Exception as e:
                print(f"Failed to save debug image {idx}: {str(e)}")

    async def debug_page_extraction(self, page_num: int):
        """Debug image extraction for a specific page"""
        print(f"\nAnalyzing page {page_num}...")
        
        try:
            # Extract images from the page
            images = await self.analyzer.extract_images_from_pdf(self.pdf_path, page_num)
            print(f"Found {len(images)} potential images on page {page_num}")
            
            # Save extracted images
            self.save_debug_images(images, page_num)
            
            # Analyze each image with Gemini
            for idx, img in enumerate(images):
                print(f"\nAnalyzing image {idx} from page {page_num}...")
                analysis = await self.analyzer.analyze_graph(
                    img,
                    f"Analysis for image {idx} on page {page_num}"
                )
                
                # Save API response
                response_path = os.path.join(self.debug_dir, f"page_{page_num}_image_{idx}_response.txt")
                with open(response_path, 'w', encoding='utf-8') as f:
                    f.write(f"Success: {analysis.get('success', False)}\n")
                    f.write(f"Error: {analysis.get('error', 'None')}\n")
                    f.write("\nRaw API Response:\n")
                    f.write(str(analysis.get('raw_response', 'No response available')))
                
                print(f"Analysis result for image {idx}:")
                print(f"Success: {analysis.get('success', False)}")
                print(f"Graph type: {analysis.get('graph_type')}")
                if not analysis.get('success'):
                    print(f"Error: {analysis.get('error')}")
                
        except Exception as e:
            print(f"Error processing page {page_num}: {str(e)}")

async def main():
    # ファイル選択ダイアログを表示
    import tkinter as tk
    from tkinter import filedialog

    root = tk.Tk()
    root.withdraw()  # メインウィンドウを非表示

    print("PDFファイルを選択してください...")
    pdf_path = filedialog.askopenfilename(
        title="分析するPDFファイルを選択",
        filetypes=[("PDF files", "*.pdf")],
        initialdir="~"  # ホームディレクトリから開始
    )

    if not pdf_path:
        print("ファイルが選択されませんでした。")
        return

    print(f"\n選択されたファイル: {pdf_path}")
    debugger = GraphExtractionDebugger(pdf_path)
    
    # PDFの全ページを分析
    import fitz
    doc = fitz.open(pdf_path)
    page_count = len(doc)
    doc.close()
    
    print(f"PDFの総ページ数: {page_count}")
    for page_num in range(page_count):
        await debugger.debug_page_extraction(page_num)
        print(f"\nページ {page_num + 1}/{page_count} の分析が完了しました")

if __name__ == "__main__":
    asyncio.run(main())