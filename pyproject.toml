[project]
name = "exam-marker"
version = "0.0.1"
description = "Automated exam grading system using Google's Gemini API"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
dependencies = [
    "google-generativeai>=0.3.0",
    "python-dotenv>=1.0.0",
    "PyMuPDF>=1.23.0",
    "pillow>=10.0.0",  # Added for graph analysis
    "numpy>=1.24.0",   # Added for numerical operations
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
